import streamlit as st
import pandas as pd
from datetime import datetime, time, timedelta
import os
import plotly.express as px
from collections import defaultdict
import numpy as np

# --- Page setup ---
st.set_page_config(layout="wide")

left_logo = r"C:\Users\<USER>\Downloads\images.jpg"  # Replace with your logo path or URL
right_logo =r"C:\Users\<USER>\Downloads\images.jpg" 
right_logo1 =r"C:\Users\<USER>\Downloads\images.jpg"# Replace with your logo path or URL
col1, col2, col3 = st.columns([1, 6, 1])
with col1: 
                st.image(left_logo, width=120)
with col2:
                st.markdown("<h1 style='text-align: center; font-size: 30px;'>Aditya Technological PTA Breakdown Analysis Application</h1>", unsafe_allow_html=True)
with col3: 
                st.image(right_logo, width=120)



# --- Excel file for data source ---
EXCEL_FILE = "PTA BD ANALYSIS FY-21-22.xlsx"

# --- Load existing data from Excel ---
if os.path.exists(EXCEL_FILE):
    try:
        df_existing = pd.read_excel(EXCEL_FILE, sheet_name='PTA', header=1)
        # Clean up the data - keep only relevant columns
        relevant_columns = ['DATE', 'EQUIPMENT', 'SUB-EQUIPMENT', 'PROBLEM', 'CATEGORY', 'E/M', 
                          'PROBLEM DESCRIPTION', 'ACTION TAKEN', 'START TIME', 'END TIME', 
                          'TOTAL TIME', 'PERMIT NO.', 'SPARES CONSUMED', 'REMARKS', 'I/C']
        df_existing = df_existing[relevant_columns].dropna(subset=['DATE'])
        
        # Convert DATE to datetime if not already
        df_existing['DATE'] = pd.to_datetime(df_existing['DATE'])
        
    except Exception as e:
        st.error(f"Error reading Excel file: {e}")
        df_existing = pd.DataFrame(columns=relevant_columns)
else:
    df_existing = pd.DataFrame(columns=[
        'DATE', 'EQUIPMENT', 'SUB-EQUIPMENT', 'PROBLEM', 'CATEGORY', 'E/M', 
        'PROBLEM DESCRIPTION', 'ACTION TAKEN', 'START TIME', 'END TIME', 
        'TOTAL TIME', 'PERMIT NO.', 'SPARES CONSUMED', 'REMARKS', 'I/C'
    ])

def save_to_excel(df):
    # For now, we'll just display success message as we're reading from existing Excel
    # In a real implementation, you might want to append to a separate sheet or file
    pass



tab1,tab2=st.tabs(["e-Form Page","Analytics Overview"])

with tab1:
    st.markdown(
    "<div style='text-align: center; font-size:22px; font-weight: bold; color: blue;'>🚧 PTA Breakdown Analysis Log</div>",
    unsafe_allow_html=True)
    # --- PTA Equipment mapping based on Excel data ---
    asset_mapping = defaultdict(list)
    asset_pairs = [
        ("PTA", "PTA 1"), ("PTA", "PTA 2"), ("PTA", "PTA 3"), ("PTA", "PTA 4"), ("PTA", "PTA 5"),
        ("PTA", "PTA 6"), ("PTA", "PTA 7"), ("PTA", "PTA 8"), ("PTA", "PTA 9"), 
    ]
    for cat, name in asset_pairs:
        asset_mapping[cat].append(name)

    # --- PTA Equipment → (Sub-Equipment, Problems) mapping based on Excel data ---
    # Mapping of sub-equipment to problem categories based on Excel analysis
    sub_equipment_problem_mapping = {
        "EXTRACTION": [
            "GRAB OPENING PROBLEM", "WRENCH NOT GOING UP", "WRENCH LOCKING",
            "ANTIFALL HOOK", "SLIP", "HYDRO MOTOR", "AIR LEAKAGE", "OIL LEAKAGE"
        ],
        "TAPPING HOIST": [
            "ABRF BELL", "HOOK ROTATION", "ROPE UNWIND"
        ],
        "BATHPIPE": [
            "MATERIAL DISCHARGE PROBLEM", "ROPE UNWIND", "FLEXIBLE HOSE LOCKING PROBLEM",
            "ELBOW DAMAGE", "FILLING PROBLEM", "GATE VALVE OPENING PROBLEM", "PARALLEL SENSOR"
        ],
        "LT": [
            "ABNORMAL SOUND FROM WHEEL", "LT BRAKE", "METAL PIECE IN RAIL",
            "RAIL CLEANING", "TOOLS INCLINATION"
        ],
        "CABIN": [
            "GLASS CLEANING", "CABIN DOOR", "LADDER PROBLEM"
        ],
        "SHOVEL": [
            "1ST LIFTING JERK", "1ST LIFTING SLIP", "2ND LIFTING SLIP", "BUCKET",
            "EXTENSION ROD", "HYDRAULIC OIL LEAKAGE", "LEAKAGE FROM FITTING",
            "MATERIAL DISCHARGE PROBLEM", "OIL LEAKAGE", "OPEN CLOSE BLOCK CHANGE",
            "OPEN CLOSE COUNTERBALANCE", "SLIP", "TOOLS SLOW"
        ],
        "COMPRESSOR": [
            "AIR LEAKAGE", "AUTO DRAIN VALVE", "L.C OIL TEMP", "MOISTURE",
            "OIL LEAKAGE", "U.C LOW PRESSURE"
        ],
        "CRUST BREAKER": [
            "AIR LEAKAGE", "BREAKING PROBLEM", "DRAG CHAIN", "HAMMER TIP DAMAGE",
            "HOSE DISCONNECTED"
        ],
        "CT": [
            "ABNORMAL SOUND FROM WHEEL", "RAIL CLEANING"
        ],
        "HYDRAULIC UNIT": [
            "HYDRAULIC TOOL NOT OPERATING", "OIL LEAKAGE"
        ],
        "TOOLS INCLINATION": [
            "PIN PROBLEM"
        ]
    }

    # Create the category_components structure
    category_components = {
        "PTA": [(sub_eq, list(problems)) for sub_eq, problems in sub_equipment_problem_mapping.items()]
    }

    # E/M categories based on industry standards and Excel data
    em_categories = ["Mechanical (M)", "Electrical (E)", "Instrumentation (I)", "A/C (AC)"]

    # --- Initialize session_state keys ---
    form_keys = {
        "breakdown_date": datetime.today().date(),
        "start_time": time(10, 0),
        "end_time": time(12, 0),
        "equipment": "PTA",
        "equipment_name": asset_mapping["PTA"][0],
        "sub_equipment": "",
        "problem_category": "",
        "em_category": "Mechanical (M)",  # Default E/M category
        "problem_description": "",
        "action_taken": "",
        "permit_no": "",
        "spares_consumed": "",
        "remarks": "",
        "ic_name": "",
        "edit_mode": False,
        "edit_index": None
    }

    for k, v in form_keys.items():
        if k not in st.session_state:
            st.session_state[k] = v

    pta_names = [
        "PTA 1", "PTA 2", "PTA 3", "PTA 4", "PTA 5", "PTA 6", "PTA 7", "PTA 8", "PTA 9"]

    # Define a list of visually distinct colors (loop will reuse if names > colors)
    colors = [
        "#1a73e8", "#d93025", "#188038", "#f9ab00", "#6f42c1", "#e37400", "#008080", "#ff6f61",
        "#4caf50", "#d81b60", "#5c6bc0", "#9c27b0", "#00897b", "#c2185b", "#607d8b", "#f57c00"
    ]

    # Create colored spans for each PTA
    pta_html = "".join([
        f"<span style='padding: 0 12px; color: {colors[i % len(colors)]};'>{name}</span>"
        for i, name in enumerate(pta_names)
    ])

    # Display the full styled row
    st.markdown(f"""
    <div style='display: flex; justify-content: center; flex-wrap: wrap; font-size: 16px; font-weight: bold; text-align: center;'>
        {pta_html}
    </div>
    """, unsafe_allow_html=True)

    # --- Create 5 columns for selectboxes ---
    col1, col2, col3, col4, col5 = st.columns(5)

    # --- Equipment Category selectbox ---
    equipment_categories = sorted(asset_mapping.keys())
    with col1:
        selected_category = st.selectbox(
            "Equipment Category", equipment_categories,
            index=equipment_categories.index(st.session_state.equipment)
        )
    if selected_category != st.session_state.equipment:
        st.session_state.equipment = selected_category
        st.session_state.equipment_name = asset_mapping[selected_category][0]
        st.session_state.sub_equipment = ""
        st.session_state.problem_category = ""

    # --- Equipment Name selectbox ---
    filtered_names = asset_mapping[st.session_state.equipment]
    with col2:
        selected_name = st.selectbox(
            "Equipment Name", filtered_names,
            index=filtered_names.index(st.session_state.equipment_name)
        )
    if selected_name != st.session_state.equipment_name:
        st.session_state.equipment_name = selected_name

    # --- Sub-Equipment selectbox ---
    comp_pos_list = category_components.get(st.session_state.equipment, [])
    component_names = [comp for comp, _ in comp_pos_list]

    # Set default sub_equipment if not valid
    if st.session_state.sub_equipment not in component_names:
        st.session_state.sub_equipment = component_names[0] if component_names else ""

    with col3:
        sub_equipment_selection = st.selectbox(
            "Sub-Equipment",
            options=component_names,
            index=component_names.index(st.session_state.sub_equipment)
        )
    if sub_equipment_selection != st.session_state.sub_equipment:
        st.session_state.sub_equipment = sub_equipment_selection
        st.session_state.problem_category = ""

    # --- Problem Category selectbox (based on selected sub-equipment) ---
    category_options = next((pos_list for comp, pos_list in comp_pos_list if comp == st.session_state.sub_equipment), [])
    if not category_options:
        category_options = ["GRAB OPENING PROBLEM"]  # Default fallback

    if st.session_state.problem_category not in category_options:
        st.session_state.problem_category = category_options[0]

    with col4:
        category_selection = st.selectbox(
            "Problem Category",
            options=category_options,
            index=category_options.index(st.session_state.problem_category)
        )
    if category_selection != st.session_state.problem_category:
        st.session_state.problem_category = category_selection

    # --- E/M Category selectbox ---
    with col5:
        em_selection = st.selectbox(
            "E/M Category",
            options=em_categories,
            index=em_categories.index(st.session_state.em_category)
        )
    if em_selection != st.session_state.em_category:
        st.session_state.em_category = em_selection


    st.markdown("---")
    st.markdown(
        "<div style='text-align: center; font-size:20px; font-weight: bold; color: red;'>🔧 Enter Breakdown Details Below</div>",
        unsafe_allow_html=True
    )

    # Show headers for the first row (date and time fields)
    detail_headers = [
        "Breakdown Date", "Start Time", "End Time", "Total Time"
    ]
    header_cols = st.columns(len(detail_headers))
    for col, header in zip(header_cols, detail_headers):
        col.markdown(f"**{header}**")

    # --- Form for the remaining fields ---

    from datetime import timedelta
    with st.form("breakdown_form"):
        # First row: Date and time fields (side by side)
        input_cols = st.columns(4)

        st.session_state.breakdown_date = input_cols[0].date_input("Breakdown Date", value=st.session_state.breakdown_date, label_visibility="collapsed")

        st.session_state.start_time = input_cols[1].time_input(
            "Start Time",
            value=st.session_state.start_time,
            step=timedelta(minutes=5),
            label_visibility="collapsed"
        )

        st.session_state.end_time = input_cols[2].time_input(
            "End Time",
            value=st.session_state.end_time,
            step=timedelta(minutes=5),
            label_visibility="collapsed"
        )


        # Duration calculation
        try:
            dt_from = datetime.combine(st.session_state.breakdown_date, st.session_state.start_time)
            dt_to = datetime.combine(st.session_state.breakdown_date, st.session_state.end_time)

            if dt_to < dt_from:
                # Handle case where end time is next day
                dt_to = datetime.combine(st.session_state.breakdown_date + timedelta(days=1), st.session_state.end_time)

            duration = dt_to - dt_from
            total_minutes = int(duration.total_seconds() // 60)
            hours, minutes = divmod(total_minutes, 60)
            duration_str = f"{hours:02d}:{minutes:02d}"
        except Exception:
            duration_str = "Invalid"

        input_cols[3].text_input("Total Time", value=duration_str, disabled=True, label_visibility="collapsed")

        # Add CSS for light blue borders on vertical input fields only
        st.markdown("""
        <style>
        .vertical-input .stTextInput > div > div > input {
            border: 2px solid #ADD8E6 !important;
            border-radius: 5px !important;
        }
        .vertical-input .stTextInput > div > div > input:focus {
            border: 2px solid #4682B4 !important;
            box-shadow: 0 0 5px rgba(70, 130, 180, 0.3) !important;
        }
        </style>
        """, unsafe_allow_html=True)

        # Vertical layout for the remaining fields with light blue borders on input fields only
        st.markdown('<div class="vertical-input">', unsafe_allow_html=True)
        st.markdown("**Problem Description**")
        st.session_state.problem_description = st.text_input("Problem Description", value=st.session_state.problem_description, label_visibility="collapsed")
        st.markdown('</div>', unsafe_allow_html=True)

        st.markdown('<div class="vertical-input">', unsafe_allow_html=True)
        st.markdown("**Action Taken**")
        st.session_state.action_taken = st.text_input("Action Taken", value=st.session_state.action_taken, label_visibility="collapsed")
        st.markdown('</div>', unsafe_allow_html=True)

        st.markdown('<div class="vertical-input">', unsafe_allow_html=True)
        st.markdown("**Permit No.**")
        st.session_state.permit_no = st.text_input("Permit No.", value=st.session_state.permit_no, label_visibility="collapsed")
        st.markdown('</div>', unsafe_allow_html=True)

        st.markdown('<div class="vertical-input">', unsafe_allow_html=True)
        st.markdown("**Spares Consumed**")
        st.session_state.spares_consumed = st.text_input("Spares Consumed", value=st.session_state.spares_consumed, label_visibility="collapsed")
        st.markdown('</div>', unsafe_allow_html=True)

        st.markdown('<div class="vertical-input">', unsafe_allow_html=True)
        st.markdown("**Remarks**")
        st.session_state.remarks = st.text_input("Remarks", value=st.session_state.remarks, label_visibility="collapsed")
        st.markdown('</div>', unsafe_allow_html=True)

        st.markdown('<div class="vertical-input">', unsafe_allow_html=True)
        st.markdown("**I/C Name**")
        st.session_state.ic_name = st.text_input("I/C Name", value=st.session_state.ic_name, label_visibility="collapsed")
        st.markdown('</div>', unsafe_allow_html=True)

        submitted = st.form_submit_button("Submit Breakdown Entry")

        if submitted:
            # Basic validation
            if not st.session_state.problem_description.strip():
                st.error("❌ Problem Description is required.")
            else:
                entry_data = {
                    "DATE": st.session_state.breakdown_date,
                    "EQUIPMENT": st.session_state.equipment_name,
                    "SUB-EQUIPMENT": st.session_state.sub_equipment,
                    "PROBLEM": st.session_state.problem_category,  # Use problem category as PROBLEM
                    "CATEGORY": "BD",  # Default category based on Excel data
                    "E/M": st.session_state.em_category,  # Use separate E/M category
                    "PROBLEM DESCRIPTION": st.session_state.problem_description,
                    "ACTION TAKEN": st.session_state.action_taken,
                    "START TIME": st.session_state.start_time,
                    "END TIME": st.session_state.end_time,
                    "TOTAL TIME": duration_str,
                    "PERMIT NO.": st.session_state.permit_no,
                    "SPARES CONSUMED": st.session_state.spares_consumed,
                    "REMARKS": st.session_state.remarks,
                    "I/C": st.session_state.ic_name
                }

                # Add new entry
                new_entry = pd.DataFrame([entry_data])
                df_existing = pd.concat([df_existing, new_entry], ignore_index=True)
                st.success("✅ Breakdown entry saved successfully!")

                save_to_excel(df_existing)

                # Reset form fields
                st.session_state.breakdown_date = datetime.today().date()
                st.session_state.start_time = time(10, 0)
                st.session_state.end_time = time(12, 0)
                st.session_state.problem_description = ""
                st.session_state.action_taken = ""
                st.session_state.permit_no = ""
                st.session_state.spares_consumed = ""
                st.session_state.remarks = ""
                st.session_state.ic_name = ""
                # Keep E/M category as is
                st.rerun()



    # --- Show saved entries ---
    if not df_existing.empty:
        st.markdown(
            "<div style='text-align: center; font-size:20px; font-weight: bold; color: green;'>📋 PTA Breakdown Entries</div>",
            unsafe_allow_html=True
        )

        with st.expander("🔍 Filter Entries"):
            # Date range filter
            min_date = df_existing['DATE'].min()
            max_date = df_existing['DATE'].max()
            start_date, end_date = st.date_input("Date Range", (min_date.date(), max_date.date()))

            # Filter Equipment
            equipment_list = ["All"] + sorted(df_existing['EQUIPMENT'].dropna().unique())
            selected_equipment = st.selectbox("Equipment", equipment_list)

            # Filter Sub-Equipment based on selected equipment
            if selected_equipment != "All":
                filtered_sub_equipment = df_existing[df_existing['EQUIPMENT'] == selected_equipment]['SUB-EQUIPMENT'].dropna().unique()
            else:
                filtered_sub_equipment = df_existing['SUB-EQUIPMENT'].dropna().unique()
            sub_equipment_list = ["All"] + sorted(filtered_sub_equipment)
            selected_sub_equipment = st.selectbox("Sub-Equipment", sub_equipment_list)

            # Filter Problem Category
            problem_list = ["All"] + sorted(df_existing['PROBLEM'].dropna().unique())
            selected_problem = st.selectbox("Problem Category", problem_list)

            # Filter E/M Category
            em_list = ["All"] + sorted(df_existing['E/M'].dropna().unique())
            selected_em = st.selectbox("E/M Category", em_list)

            # Apply All Filters
            df_filtered = df_existing[
                (df_existing['DATE'].dt.date >= start_date) &
                (df_existing['DATE'].dt.date <= end_date)
            ]

            if selected_equipment != "All":
                df_filtered = df_filtered[df_filtered['EQUIPMENT'] == selected_equipment]

            if selected_sub_equipment != "All":
                df_filtered = df_filtered[df_filtered['SUB-EQUIPMENT'] == selected_sub_equipment]

            if selected_problem != "All":
                df_filtered = df_filtered[df_filtered['PROBLEM'] == selected_problem]

            if selected_em != "All":
                df_filtered = df_filtered[df_filtered['E/M'] == selected_em]

        # Sort by date and reset index for proper row identification
        df_display = df_filtered.sort_values(by="DATE", ascending=False).reset_index(drop=True)

        # Add edit/delete functionality
        st.markdown("### 📝 Breakdown Entries with Edit/Delete Options")

        # Initialize delete confirmation state
        if 'delete_confirm' not in st.session_state:
            st.session_state.delete_confirm = {}

        # Display entries with edit/delete buttons
        for idx, row in df_display.iterrows():
            with st.container():
                col1, col2, col3, col4 = st.columns([6, 1, 1, 1])

                with col1:
                    # Display entry details in a compact format
                    st.markdown(f"""
                    **Date:** {row['DATE'].strftime('%Y-%m-%d')} | **Equipment:** {row['EQUIPMENT']} | **Sub-Equipment:** {row['SUB-EQUIPMENT']}

                    **Problem:** {row['PROBLEM']} | **E/M:** {row['E/M']} | **Duration:** {row['TOTAL TIME']}

                    **Description:** {row['PROBLEM DESCRIPTION']} | **Action Taken:** {row['ACTION TAKEN']} | **I/C:** {row['I/C']}
                    """)

                with col2:
                    if st.button("✏️ Edit", key=f"edit_{idx}"):
                        st.session_state.edit_mode = True
                        st.session_state.edit_index = idx
                        # Populate form with existing data
                        original_idx = df_filtered.index[idx]  # Get original index from filtered data
                        st.session_state.breakdown_date = row['DATE'].date()
                        st.session_state.equipment_name = row['EQUIPMENT']
                        st.session_state.sub_equipment = row['SUB-EQUIPMENT']
                        st.session_state.problem_category = row['PROBLEM'] if pd.notna(row['PROBLEM']) else ""
                        st.session_state.em_category = row['E/M'] if pd.notna(row['E/M']) else "Mechanical (M)"
                        st.session_state.problem_description = row['PROBLEM DESCRIPTION']
                        st.session_state.action_taken = row['ACTION TAKEN']
                        st.session_state.permit_no = row['PERMIT NO.'] if pd.notna(row['PERMIT NO.']) else ""
                        st.session_state.spares_consumed = row['SPARES CONSUMED'] if pd.notna(row['SPARES CONSUMED']) else ""
                        st.session_state.remarks = row['REMARKS'] if pd.notna(row['REMARKS']) else ""
                        st.session_state.ic_name = row['I/C'] if pd.notna(row['I/C']) else ""
                        # Parse time fields
                        try:
                            if pd.notna(row['START TIME']):
                                if isinstance(row['START TIME'], str):
                                    st.session_state.start_time = datetime.strptime(row['START TIME'], '%H:%M:%S').time()
                                else:
                                    st.session_state.start_time = row['START TIME']
                            if pd.notna(row['END TIME']):
                                if isinstance(row['END TIME'], str):
                                    st.session_state.end_time = datetime.strptime(row['END TIME'], '%H:%M:%S').time()
                                else:
                                    st.session_state.end_time = row['END TIME']
                        except:
                            pass
                        st.rerun()

                with col3:
                    if idx not in st.session_state.delete_confirm:
                        if st.button("🗑️ Delete", key=f"delete_{idx}"):
                            st.session_state.delete_confirm[idx] = True
                            st.rerun()
                    else:
                        if st.button("✅ Confirm", key=f"confirm_delete_{idx}"):
                            # Delete the entry
                            original_idx = df_filtered.index[idx]
                            df_existing.drop(original_idx, inplace=True)
                            df_existing.reset_index(drop=True, inplace=True)
                            # Save changes (in real implementation, save to Excel)
                            save_to_excel(df_existing)
                            st.success(f"Entry deleted successfully!")
                            del st.session_state.delete_confirm[idx]
                            st.rerun()

                with col4:
                    if idx in st.session_state.delete_confirm:
                        if st.button("❌ Cancel", key=f"cancel_delete_{idx}"):
                            del st.session_state.delete_confirm[idx]
                            st.rerun()

                st.markdown("---")

        # Edit form (appears when edit button is clicked)
        if st.session_state.edit_mode and st.session_state.edit_index is not None:
            st.markdown("### ✏️ Edit Breakdown Entry")

            with st.form("edit_form"):
                edit_cols = st.columns(3)

                with edit_cols[0]:
                    edit_date = st.date_input("Breakdown Date", value=st.session_state.breakdown_date)
                    edit_start_time = st.time_input("Start Time", value=st.session_state.start_time)
                    edit_end_time = st.time_input("End Time", value=st.session_state.end_time)
                    edit_equipment = st.selectbox("Equipment", asset_mapping["PTA"],
                                                index=asset_mapping["PTA"].index(st.session_state.equipment_name))

                with edit_cols[1]:
                    edit_sub_equipment = st.selectbox("Sub-Equipment",
                                                    [comp for comp, _ in category_components["PTA"]],
                                                    index=[comp for comp, _ in category_components["PTA"]].index(st.session_state.sub_equipment))

                    # Get problem categories for selected sub-equipment
                    sub_eq_problems = next((problems for sub_eq, problems in category_components["PTA"]
                                          if sub_eq == edit_sub_equipment), [])

                    # Handle case where problem category is not in the list
                    if st.session_state.problem_category in sub_eq_problems:
                        problem_index = sub_eq_problems.index(st.session_state.problem_category)
                    else:
                        problem_index = 0

                    edit_category = st.selectbox("Problem Category", sub_eq_problems, index=problem_index)

                    # E/M Category dropdown
                    em_index = em_categories.index(st.session_state.em_category) if st.session_state.em_category in em_categories else 0
                    edit_em_category = st.selectbox("E/M Category", em_categories, index=em_index)

                    edit_problem_desc = st.text_area("Problem Description", value=st.session_state.problem_description)
                    edit_action_taken = st.text_area("Action Taken", value=st.session_state.action_taken)

                with edit_cols[2]:
                    edit_permit_no = st.text_input("Permit No.", value=st.session_state.permit_no)
                    edit_spares = st.text_input("Spares Consumed", value=st.session_state.spares_consumed)
                    edit_remarks = st.text_input("Remarks", value=st.session_state.remarks)
                    edit_ic_name = st.text_input("I/C Name", value=st.session_state.ic_name)

                col_save, col_cancel = st.columns(2)
                with col_save:
                    save_edit = st.form_submit_button("💾 Save Changes")
                with col_cancel:
                    cancel_edit = st.form_submit_button("❌ Cancel Edit")

                if save_edit:
                    # Calculate duration
                    try:
                        dt_from = datetime.combine(edit_date, edit_start_time)
                        dt_to = datetime.combine(edit_date, edit_end_time)
                        if dt_to < dt_from:
                            dt_to = datetime.combine(edit_date + timedelta(days=1), edit_end_time)
                        duration = dt_to - dt_from
                        total_minutes = int(duration.total_seconds() // 60)
                        hours, minutes = divmod(total_minutes, 60)
                        duration_str = f"{hours:02d}:{minutes:02d}"
                    except:
                        duration_str = "00:00"

                    # Update the entry
                    original_idx = df_filtered.index[st.session_state.edit_index]
                    df_existing.loc[original_idx, 'DATE'] = edit_date
                    df_existing.loc[original_idx, 'EQUIPMENT'] = edit_equipment
                    df_existing.loc[original_idx, 'SUB-EQUIPMENT'] = edit_sub_equipment
                    df_existing.loc[original_idx, 'PROBLEM'] = edit_category  # Use problem category as PROBLEM
                    df_existing.loc[original_idx, 'CATEGORY'] = "BD"  # Default category
                    df_existing.loc[original_idx, 'E/M'] = edit_em_category  # Use separate E/M category
                    df_existing.loc[original_idx, 'PROBLEM DESCRIPTION'] = edit_problem_desc
                    df_existing.loc[original_idx, 'ACTION TAKEN'] = edit_action_taken
                    df_existing.loc[original_idx, 'START TIME'] = edit_start_time
                    df_existing.loc[original_idx, 'END TIME'] = edit_end_time
                    df_existing.loc[original_idx, 'TOTAL TIME'] = duration_str
                    df_existing.loc[original_idx, 'PERMIT NO.'] = edit_permit_no
                    df_existing.loc[original_idx, 'SPARES CONSUMED'] = edit_spares
                    df_existing.loc[original_idx, 'REMARKS'] = edit_remarks
                    df_existing.loc[original_idx, 'I/C'] = edit_ic_name

                    # Save changes (in real implementation, save to Excel)
                    save_to_excel(df_existing)
                    st.success("Entry updated successfully!")
                    st.session_state.edit_mode = False
                    st.session_state.edit_index = None
                    st.rerun()

                if cancel_edit:
                    st.session_state.edit_mode = False
                    st.session_state.edit_index = None
                    st.rerun()

        if st.button("Mail the Report") and not df_display.empty:
            st.info("Email functionality would be implemented here for sending PTA breakdown reports.")

with tab2:
     st.markdown(
    "<div style='text-align: center; font-size:22px; font-weight: bold; color: blue;'>🚧 PTA Breakdown Analysis</div>",
    unsafe_allow_html=True)

     df = df_existing.copy()

     def convert_duration(x):
        try:
            if isinstance(x, str) and ':' in x:
                h, m = map(int, x.split(":"))
                return h + m/60
            return float(x)
        except:
            return np.nan

     if not df.empty:
         df['Duration_hr'] = df['TOTAL TIME'].apply(convert_duration)
         df['Month'] = df['DATE'].dt.to_period('M').astype(str)

     tabs = st.tabs(["📄 Raw Data", "📊 Analytics"])

    # ============================ Tab 1: Raw Data
     with tabs[0]:
        st.subheader("PTA Breakdown Entries")
        st.dataframe(df_existing, use_container_width=True)

    # ============================ Tab 2: Analytics
     with tabs[1]:
        if not df.empty:
            col1, col2 = st.columns(2)

            # 1️⃣ Equipment-wise Breakdown Frequency
            with col1:
                st.markdown("### 📊 Equipment-wise Breakdown Frequency")
                equip_df = df.groupby('EQUIPMENT').size().reset_index(name='Breakdown Count')
                fig_equip = px.bar(equip_df, x="EQUIPMENT", y="Breakdown Count",
                                 color="EQUIPMENT", title="Breakdowns by Equipment")
                st.plotly_chart(fig_equip, use_container_width=True)

            # 2️⃣ Sub-Equipment Analysis
            with col2:
                st.markdown("### 🔧 Top Sub-Equipment Breakdowns")
                sub_equip_df = df.groupby('SUB-EQUIPMENT').size().reset_index(name='Count').sort_values('Count', ascending=False).head(10)
                fig_sub = px.bar(sub_equip_df, x="Count", y="SUB-EQUIPMENT",
                               orientation='h', title="Top 10 Sub-Equipment Breakdowns")
                st.plotly_chart(fig_sub, use_container_width=True)

            # 3️⃣ Monthly Breakdown Trend
            st.markdown("### 📈 Monthly Breakdown Trend")
            trend_df = df.groupby('Month').size().reset_index(name='Breakdown Count')
            fig_trend = px.line(trend_df, x='Month', y='Breakdown Count', markers=True,
                              title="Monthly Breakdown Trend")
            st.plotly_chart(fig_trend, use_container_width=True)

            # 4️⃣ Problem Category Analysis (E/M)
            col3, col4 = st.columns(2)
            with col3:
                st.markdown("### ⚡ Electrical vs Mechanical Breakdowns")
                category_df = df.groupby('CATEGORY').size().reset_index(name='Count')
                fig_category = px.pie(category_df, values='Count', names='CATEGORY',
                                    title="Breakdown Distribution by Category")
                st.plotly_chart(fig_category, use_container_width=True)

            # 5️⃣ Average Breakdown Duration by Equipment
            with col4:
                st.markdown("### ⏱️ Average Breakdown Duration")
                duration_df = df.groupby('EQUIPMENT')['Duration_hr'].mean().reset_index()
                duration_df = duration_df.sort_values('Duration_hr', ascending=False)
                fig_duration = px.bar(duration_df, x="EQUIPMENT", y="Duration_hr",
                                    title="Average Breakdown Duration (Hours)")
                st.plotly_chart(fig_duration, use_container_width=True)

            # 6️⃣ MTTR Analysis (Mean Time To Repair)
            st.markdown("### 📊 MTTR Analysis by Equipment")
            if 'Duration_hr' in df.columns:
                mttr_df = df.groupby('EQUIPMENT')['Duration_hr'].agg(['mean', 'count', 'std']).reset_index()
                mttr_df.columns = ['Equipment', 'MTTR (Hours)', 'Breakdown Count', 'Std Dev']
                mttr_df = mttr_df.round(2)
                st.dataframe(mttr_df, use_container_width=True)

            # 7️⃣ Top Problems Analysis
            st.markdown("### 🚨 Most Frequent Problems")
            problem_df = df.groupby('PROBLEM DESCRIPTION').size().reset_index(name='Frequency').sort_values('Frequency', ascending=False).head(10)
            if not problem_df.empty:
                fig_problems = px.bar(problem_df, x="Frequency", y="PROBLEM DESCRIPTION",
                                    orientation='h', title="Top 10 Most Frequent Problems")
                st.plotly_chart(fig_problems, use_container_width=True)
        else:
            st.info("No data available for analysis. Please add some breakdown entries first.")
