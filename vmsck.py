import streamlit as st
import pandas as pd
from datetime import datetime, time, timedelta
import os
import plotly.express as px
from collections import defaultdict
import numpy as np

# --- Page setup ---
st.set_page_config(layout="wide")

left_logo = r"C:\Users\<USER>\Downloads\images.jpg"  # Replace with your logo path or URL
right_logo = r"C:\Users\<USER>\Downloads\images.jpg" 
col1, col2, col3 = st.columns([1, 6, 1])
with col1: 
    st.image(left_logo, width=120)
with col2:
    st.markdown("<h1 style='text-align: center; font-size: 30px;'>Aditya Technological PTA Breakdown Analysis Application</h1>", unsafe_allow_html=True)
with col3: 
    st.image(right_logo, width=120)

# --- Excel file for data source ---
EXCEL_FILE = "new_pta_breakdown_data.xlsx"  # Update with your new Excel file name

# --- Load existing data from Excel and store in session state ---
def load_data_from_excel():
    """Load data from Excel file with flexible column handling"""
    relevant_columns = ['Date', 'Equipment', 'SubEquipment', 'Problem', 'Category', 'EM_Category',
                       'Problem_Desc', 'Action', 'Start_Time', 'End_Time', 'Duration', 
                       'Permit', 'Spares', 'Remarks', 'IC']

    if os.path.exists(EXCEL_FILE):
        try:
            df_existing = pd.read_excel(EXCEL_FILE, sheet_name=0, header=0)
            
            if df_existing is None or df_existing.empty:
                return pd.DataFrame(columns=relevant_columns)

            # Check available columns
            available_columns = df_existing.columns.tolist()
            named_columns = [col for col in available_columns if not str(col).startswith('Unnamed')]

            # Create column mapping
            column_mapping = {}
            for col in relevant_columns:
                if col in available_columns:
                    column_mapping[col] = col
                else:
                    for avail_col in available_columns:
                        if str(avail_col).startswith('Unnamed'):
                            continue
                        col_str = str(col).lower()
                        avail_col_str = str(avail_col).lower()
                        if col_str in avail_col_str or avail_col_str in col_str:
                            column_mapping[col] = avail_col
                            break

            # Select and rename columns
            selected_data = pd.DataFrame()
            for target_col, source_col in column_mapping.items():
                if source_col in df_existing.columns:
                    try:
                        selected_data[target_col] = df_existing[source_col]
                    except Exception as e:
                        st.warning(f"Error copying column {source_col}: {e}")
                        selected_data[target_col] = ""

            # Add missing columns
            for col in relevant_columns:
                if col not in selected_data.columns:
                    selected_data[col] = ""

            # Handle date conversion
            if 'Date' in selected_data.columns and not selected_data.empty:
                selected_data = selected_data.dropna(subset=['Date'])
                try:
                    selected_data['Date'] = pd.to_datetime(selected_data['Date'], errors='coerce')
                    selected_data = selected_data.dropna(subset=['Date'])
                except Exception as e:
                    st.warning(f"Error processing dates: {e}")
                    return pd.DataFrame(columns=relevant_columns)

            return selected_data

        except Exception as e:
            st.error(f"Error reading Excel file: {e}")
            return pd.DataFrame(columns=relevant_columns)
    else:
        return pd.DataFrame(columns=relevant_columns)

# Initialize or refresh data
if 'df_existing' not in st.session_state:
    st.session_state.df_existing = load_data_from_excel()

# Manual refresh button
if st.button("🔄 Refresh Data from Excel"):
    current_session_data = st.session_state.df_existing.copy() if not st.session_state.df_existing.empty else pd.DataFrame()
    fresh_excel_data = load_data_from_excel()
    
    if not fresh_excel_data.empty:
        if not current_session_data.empty:
            combined_data = pd.concat([fresh_excel_data, current_session_data], ignore_index=True)
            combined_data = combined_data.drop_duplicates(
                subset=['Date', 'Equipment', 'SubEquipment', 'Problem_Desc'],
                keep='last'
            )
            st.session_state.df_existing = combined_data.sort_values(by="Date", ascending=False).reset_index(drop=True)
            st.success(f"✅ Merged data: {len(fresh_excel_data)} from Excel + {len(current_session_data)} from session = {len(st.session_state.df_existing)} total entries")
        else:
            st.session_state.df_existing = fresh_excel_data
            st.success(f"✅ Loaded {len(fresh_excel_data)} entries from Excel file")
    else:
        if not current_session_data.empty:
            st.session_state.df_existing = current_session_data
            st.warning("⚠️ No valid data found in Excel file. Keeping session entries.")
        else:
            st.error("❌ No data found in Excel file and no session data available.")

df_existing = st.session_state.df_existing

# Debug and force reload
col1, col2 = st.columns(2)
with col1:
    if st.button("🔒 Force Reload from Excel (WILL LOSE SESSION DATA)", type="secondary"):
        if st.checkbox("⚠️ I understand this will delete my unsaved entries"):
            st.session_state.df_existing = load_data_from_excel()
            st.warning("⚠️ Data reloaded from Excel. Session entries were lost.")
            st.rerun()

with col2:
    if st.checkbox("🔍 Show Debug Info"):
        st.write("### 📊 Current Data Status")
        st.write(f"**Current session data shape:** {df_existing.shape}")
        if not df_existing.empty:
            st.write(f"**Latest entry date:** {df_existing['Date'].max()}")
            st.write(f"**Date range:** {df_existing['Date'].min()} to {df_existing['Date'].max()}")
            st.write(f"**Unique years:** {sorted(df_existing['Date'].dt.year.unique())}")
            st.write(f"**Columns:** {list(df_existing.columns)}")
            st.write("**Latest 3 entries:**")
            st.dataframe(df_existing.head(3))

def save_to_excel(df):
    """Enhanced save function with multiple fallback options"""
    try:
        if not df.empty and 'Date' in df.columns:
            df['Date'] = pd.to_datetime(df['Date'])
        df.to_excel(EXCEL_FILE, sheet_name='Sheet1', index=False)
        st.success(f"✅ Data saved to Excel: {EXCEL_FILE}")
        return True
    except Exception as e:
        st.error(f"Error saving to Excel: {e}")
        return False

tab1, tab2 = st.tabs(["e-Form Page", "Analytics Overview"])

with tab1:
    st.markdown(
        "<div style='text-align: center; font-size:22px; font-weight: bold; color: blue;'>🚧 PTA Breakdown Analysis Log</div>",
        unsafe_allow_html=True
    )

    # --- PTA Equipment mapping (update based on new Excel data) ---
    asset_mapping = defaultdict(list)
    if not df_existing.empty:
        unique_equipments = df_existing['Equipment'].dropna().unique()
        for eq in unique_equipments:
            asset_mapping["PTA"].append(eq)
    else:
        asset_mapping["PTA"] = ["PTA 1", "PTA 2", "PTA 3", "PTA 4", "PTA 5", "PTA 6", "PTA 7", "PTA 8", "PTA 9"]

    # --- Sub-Equipment and Problem mapping (update based on new Excel data) ---
    sub_equipment_problem_mapping = {}
    if not df_existing.empty:
        for sub_eq in df_existing['SubEquipment'].dropna().unique():
            problems = df_existing[df_existing['SubEquipment'] == sub_eq]['Problem'].dropna().unique().tolist()
            sub_equipment_problem_mapping[sub_eq] = problems if problems else ["Unknown Problem"]
    else:
        sub_equipment_problem_mapping = {
            "Unknown Sub-Equipment": ["Unknown Problem"]
        }

    category_components = {
        "PTA": [(sub_eq, list(problems)) for sub_eq, problems in sub_equipment_problem_mapping.items()]
    }

    em_categories = ["Mechanical (M)", "Electrical (E)", "Instrumentation (I)", "A/C (AC)"]

    # --- Initialize session_state keys ---
    form_keys = {
        "breakdown_date": datetime.today().date(),
        "start_time": time(10, 0),
        "end_time": time(12, 0),
        "equipment": "PTA",
        "equipment_name": asset_mapping["PTA"][0] if asset_mapping["PTA"] else "",
        "sub_equipment": "",
        "problem_category": "",
        "em_category": "Mechanical (M)",
        "problem_description": "",
        "action_taken": "",
        "permit_no": "",
        "spares_consumed": "",
        "remarks": "",
        "ic_name": "",
        "edit_mode": False,
        "edit_index": None
    }

    for k, v in form_keys.items():
        if k not in st.session_state:
            st.session_state[k] = v

    # Display equipment names
    pta_names = asset_mapping["PTA"]
    colors = [
        "#1a73e8", "#d93025", "#188038", "#f9ab00", "#6f42c1", "#e37400", "#008080", "#ff6f61",
        "#4caf50", "#d81b60"
    ]
    pta_html = "".join([
        f"<span style='padding: 0 12px; color: {colors[i % len(colors)]};'>{name}</span>"
        for i, name in enumerate(pta_names)
    ])
    st.markdown(f"""
    <div style='display: flex; justify-content: center; flex-wrap: wrap; font-size: 16px; font-weight: bold; text-align: center;'>
        {pta_html}
    </div>
    """, unsafe_allow_html=True)

    # --- Form inputs ---
    col1, col2, col3, col4, col5 = st.columns(5)
    equipment_categories = sorted(asset_mapping.keys())
    with col1:
        selected_category = st.selectbox(
            "Equipment Category", equipment_categories,
            index=equipment_categories.index(st.session_state.equipment)
        )
    if selected_category != st.session_state.equipment:
        st.session_state.equipment = selected_category
        st.session_state.equipment_name = asset_mapping[selected_category][0] if asset_mapping[selected_category] else ""
        st.session_state.sub_equipment = ""
        st.session_state.problem_category = ""

    with col2:
        filtered_names = asset_mapping[st.session_state.equipment]
        selected_name = st.selectbox(
            "Equipment Name", filtered_names,
            index=filtered_names.index(st.session_state.equipment_name) if st.session_state.equipment_name in filtered_names else 0
        )
    if selected_name != st.session_state.equipment_name:
        st.session_state.equipment_name = selected_name

    comp_pos_list = category_components.get(st.session_state.equipment, [])
    component_names = [comp for comp, _ in comp_pos_list]
    if st.session_state.sub_equipment not in component_names and component_names:
        st.session_state.sub_equipment = component_names[0]

    with col3:
        sub_equipment_selection = st.selectbox(
            "Sub-Equipment",
            options=component_names,
            index=component_names.index(st.session_state.sub_equipment) if st.session_state.sub_equipment in component_names else 0
        )
    if sub_equipment_selection != st.session_state.sub_equipment:
        st.session_state.sub_equipment = sub_equipment_selection
        st.session_state.problem_category = ""

    category_options = next((pos_list for comp, pos_list in comp_pos_list if comp == st.session_state.sub_equipment), [])
    if not category_options:
        category_options = ["Unknown Problem"]
    if st.session_state.problem_category not in category_options:
        st.session_state.problem_category = category_options[0]

    with col4:
        category_selection = st.selectbox(
            "Problem Category",
            options=category_options,
            index=category_options.index(st.session_state.problem_category)
        )
    if category_selection != st.session_state.problem_category:
        st.session_state.problem_category = category_selection

    with col5:
        em_selection = st.selectbox(
            "E/M Category",
            options=em_categories,
            index=em_categories.index(st.session_state.em_category)
        )
    if em_selection != st.session_state.em_category:
        st.session_state.em_category = em_selection

    st.markdown("---")
    st.markdown(
        "<div style='text-align: center; font-size:20px; font-weight: bold; color: red;'>🔧 Enter Breakdown Details Below</div>",
        unsafe_allow_html=True
    )

    detail_headers = [
        "Breakdown Date", "Start Time", "End Time", "Total Time", "Remarks", "I/C Name", "Spares Consumed"
    ]
    header_cols = st.columns(len(detail_headers))
    for col, header in zip(header_cols, detail_headers):
        col.markdown(f"**{header}**")

    with st.form("breakdown_form"):
        input_cols = st.columns(7)
        st.session_state.breakdown_date = input_cols[0].date_input("Breakdown Date", value=st.session_state.breakdown_date, label_visibility="collapsed")
        st.session_state.start_time = input_cols[1].time_input(
            "Start Time",
            value=st.session_state.start_time,
            step=timedelta(minutes=5),
            label_visibility="collapsed"
        )
        st.session_state.end_time = input_cols[2].time_input(
            "End Time",
            value=st.session_state.end_time,
            step=timedelta(minutes=5),
            label_visibility="collapsed"
        )

        try:
            dt_from = datetime.combine(st.session_state.breakdown_date, st.session_state.start_time)
            dt_to = datetime.combine(st.session_state.breakdown_date, st.session_state.end_time)
            if dt_to < dt_from:
                dt_to = datetime.combine(st.session_state.breakdown_date + timedelta(days=1), st.session_state.end_time)
            duration = dt_to - dt_from
            total_minutes = int(duration.total_seconds() // 60)
            hours, minutes = divmod(total_minutes, 60)
            duration_str = f"{hours:02d}:{minutes:02d}"
        except:
            duration_str = "Invalid"

        input_cols[3].text_input("Total Time", value=duration_str, disabled=True, label_visibility="collapsed")
        st.session_state.remarks = input_cols[4].text_input("Remarks", value=st.session_state.remarks, label_visibility="collapsed")
        st.session_state.ic_name = input_cols[5].text_input("I/C Name", value=st.session_state.ic_name, label_visibility="collapsed")
        st.session_state.spares_consumed = input_cols[6].text_input("Spares Consumed", value=st.session_state.spares_consumed, label_visibility="collapsed")

        st.markdown("""
        <style>
        div[data-testid="stForm"] .vertical-input input[type="text"] {
            border: 2px solid #ADD8E6 !important;
            border-radius: 5px !important;
        }
        div[data-testid="stForm"] .vertical-input input[type="text"]:focus {
            border: 2px solid #4682B4 !important;
            box-shadow: 0 0 5px rgba(70, 130, 180, 0.3) !important;
            outline: none !important;
        }
        .vertical-input [data-testid="stTextInput"] input {
            border: 2px solid #ADD8E6 !important;
            border-radius: 5px !important;
        }
        .vertical-input [data-testid="stTextInput"] input:focus {
            border: 2px solid #4682B4 !important;
            box-shadow: 0 0 5px rgba(70, 130, 180, 0.3) !important;
            outline: none !important;
        }
        .vertical-input .stTextInput > div > div > input {
            border: 2px solid #ADD8E6 !important;
            border-radius: 5px !important;
        }
        .vertical-input .stTextInput > div > div > input:focus {
            border: 2px solid #4682B4 !important;
            box-shadow: 0 0 5px rgba(70, 130, 180, 0.3) !important;
            outline: none !important;
        }
        </style>
        """, unsafe_allow_html=True)

        st.markdown('<div class="vertical-input">', unsafe_allow_html=True)
        st.markdown("**Problem Description**")
        st.session_state.problem_description = st.text_input("Problem Description", value=st.session_state.problem_description, label_visibility="collapsed")
        st.markdown('</div>', unsafe_allow_html=True)

        st.markdown('<div class="vertical-input">', unsafe_allow_html=True)
        st.markdown("**Action Taken**")
        st.session_state.action_taken = st.text_input("Action Taken", value=st.session_state.action_taken, label_visibility="collapsed")
        st.markdown('</div>', unsafe_allow_html=True)

        st.markdown('<div class="vertical-input">', unsafe_allow_html=True)
        st.markdown("**Permit No.**")
        st.session_state.permit_no = st.text_input("Permit No.", value=st.session_state.permit_no, label_visibility="collapsed")
        st.markdown('</div>', unsafe_allow_html=True)

        submitted = st.form_submit_button("Submit Breakdown Entry")
        if submitted:
            if not st.session_state.problem_description.strip():
                st.error("❌ Problem Description is required.")
            else:
                entry_data = {
                    "Date": pd.Timestamp(st.session_state.breakdown_date),
                    "Equipment": st.session_state.equipment_name,
                    "SubEquipment": st.session_state.sub_equipment,
                    "Problem": st.session_state.problem_category,
                    "Category": "BD",
                    "EM_Category": st.session_state.em_category,
                    "Problem_Desc": st.session_state.problem_description,
                    "Action": st.session_state.action_taken,
                    "Start_Time": st.session_state.start_time,
                    "End_Time": st.session_state.end_time,
                    "Duration": duration_str,
                    "Permit": st.session_state.permit_no,
                    "Spares": st.session_state.spares_consumed,
                    "Remarks": st.session_state.remarks,
                    "IC": st.session_state.ic_name
                }
                new_entry = pd.DataFrame([entry_data])
                st.session_state.df_existing = pd.concat([st.session_state.df_existing, new_entry], ignore_index=True)
                save_success = save_to_excel(st.session_state.df_existing)
                if save_success:
                    st.success("✅ Breakdown entry saved successfully!")
                else:
                    st.warning("⚠️ Entry saved in session but there was an issue saving to Excel file.")
                st.session_state.breakdown_date = datetime.today().date()
                st.session_state.start_time = time(10, 0)
                st.session_state.end_time = time(12, 0)
                st.session_state.problem_description = ""
                st.session_state.action_taken = ""
                st.session_state.permit_no = ""
                st.session_state.spares_consumed = ""
                st.session_state.remarks = ""
                st.session_state.ic_name = ""
                st.rerun()

    if not df_existing.empty:
        st.markdown(
            "<div style='text-align: center; font-size:20px; font-weight: bold; color: green;'>📋 PTA Breakdown Entries</div>",
            unsafe_allow_html=True
        )
        with st.expander("🔍 Filter Entries"):
            min_date = df_existing['Date'].min().date()
            max_date = df_existing['Date'].max().date()
            start_date, end_date = st.date_input("Date Range", (min_date, max_date))
            equipment_list = ["All"] + sorted(df_existing['Equipment'].dropna().unique())
            selected_equipment = st.selectbox("Equipment", equipment_list)
            if selected_equipment != "All":
                filtered_sub_equipment = df_existing[df_existing['Equipment'] == selected_equipment]['SubEquipment'].dropna().unique()
            else:
                filtered_sub_equipment = df_existing['SubEquipment'].dropna().unique()
            sub_equipment_list = ["All"] + sorted(filtered_sub_equipment)
            selected_sub_equipment = st.selectbox("Sub-Equipment", sub_equipment_list)
            problem_list = ["All"] + sorted(df_existing['Problem'].dropna().unique())
            selected_problem = st.selectbox("Problem Category", problem_list)
            em_list = ["All"] + sorted(df_existing['EM_Category'].dropna().unique())
            selected_em = st.selectbox("E/M Category", em_list)

            start_ts = pd.Timestamp(start_date)
            end_ts = pd.Timestamp(end_date) + pd.Timedelta(days=1) - pd.Timedelta(seconds=1)
            df_filtered = df_existing[
                (df_existing['Date'] >= start_ts) &
                (df_existing['Date'] <= end_ts)
            ]
            if selected_equipment != "All":
                df_filtered = df_filtered[df_filtered['Equipment'] == selected_equipment]
            if selected_sub_equipment != "All":
                df_filtered = df_filtered[df_filtered['SubEquipment'] == selected_sub_equipment]
            if selected_problem != "All":
                df_filtered = df_filtered[df_filtered['Problem'] == selected_problem]
            if selected_em != "All":
                df_filtered = df_filtered[df_filtered['EM_Category'] == selected_em]

        df_display = df_filtered.sort_values(by="Date", ascending=False).reset_index(drop=True).head(5)
        st.markdown("### 📝 Latest 5 Breakdown Entries")
        if 'delete_confirm' not in st.session_state:
            st.session_state.delete_confirm = {}

        for idx, row in df_display.iterrows():
            with st.container():
                col1, col2, col3, col4 = st.columns([6, 1, 1, 1])
                with col1:
                    st.markdown(f"""
                    **Date:** {row['Date'].strftime('%Y-%m-%d')} | **Equipment:** {row['Equipment']} | **Sub-Equipment:** {row['SubEquipment']}
                    **Problem:** {row['Problem']} | **E/M:** {row['EM_Category']} | **Duration:** {row['Duration']}
                    **Description:** {row['Problem_Desc']} | **Action Taken:** {row['Action']} | **I/C:** {row['IC']}
                    """)
                with col2:
                    if st.button("✏️ Edit", key=f"edit_{idx}"):
                        st.session_state.edit_mode = True
                        st.session_state.edit_index = idx
                        original_idx = df_filtered.index[idx]
                        st.session_state.breakdown_date = row['Date'].date()
                        st.session_state.equipment_name = row['Equipment']
                        st.session_state.sub_equipment = row['SubEquipment']
                        st.session_state.problem_category = row['Problem'] if pd.notna(row['Problem']) else ""
                        st.session_state.em_category = row['EM_Category'] if pd.notna(row['EM_Category']) else "Mechanical (M)"
                        st.session_state.problem_description = row['Problem_Desc']
                        st.session_state.action_taken = row['Action']
                        st.session_state.permit_no = row['Permit'] if pd.notna(row['Permit']) else ""
                        st.session_state.spares_consumed = row['Spares'] if pd.notna(row['Spares']) else ""
                        st.session_state.remarks = row['Remarks'] if pd.notna(row['Remarks']) else ""
                        st.session_state.ic_name = row['IC'] if pd.notna(row['IC']) else ""
                        try:
                            if pd.notna(row['Start_Time']):
                                if isinstance(row['Start_Time'], str):
                                    st.session_state.start_time = datetime.strptime(row['Start_Time'], '%H:%M:%S').time()
                                else:
                                    st.session_state.start_time = row['Start_Time']
                            if pd.notna(row['End_Time']):
                                if isinstance(row['End_Time'], str):
                                    st.session_state.end_time = datetime.strptime(row['End_Time'], '%H:%M:%S').time()
                                else:
                                    st.session_state.end_time = row['End_Time']
                        except:
                            pass
                        st.rerun()
                with col3:
                    if idx not in st.session_state.delete_confirm:
                        if st.button("🗑️ Delete", key=f"delete_{idx}"):
                            st.session_state.delete_confirm[idx] = True
                            st.rerun()
                    else:
                        if st.button("✅ Confirm", key=f"confirm_delete_{idx}"):
                            original_idx = df_filtered.index[idx]
                            st.session_state.df_existing.drop(original_idx, inplace=True)
                            st.session_state.df_existing.reset_index(drop=True, inplace=True)
                            df_existing = st.session_state.df_existing
                            save_success = save_to_excel(df_existing)
                            if save_success:
                                st.success(f"Entry deleted successfully!")
                            else:
                                st.warning("Entry deleted from session but there was an issue updating Excel file.")
                            del st.session_state.delete_confirm[idx]
                            st.rerun()
                with col4:
                    if idx in st.session_state.delete_confirm:
                        if st.button("❌ Cancel", key=f"cancel_delete_{idx}"):
                            del st.session_state.delete_confirm[idx]
                            st.rerun()
                st.markdown("---")

        if st.session_state.edit_mode and st.session_state.edit_index is not None:
            st.markdown("### ✏️ Edit Breakdown Entry")
            with st.form("edit_form"):
                edit_cols = st.columns(3)
                with edit_cols[0]:
                    edit_date = st.date_input("Breakdown Date", value=st.session_state.breakdown_date)
                    edit_start_time = st.time_input("Start Time", value=st.session_state.start_time)
                    edit_end_time = st.time_input("End Time", value=st.session_state.end_time)
                    edit_equipment = st.selectbox("Equipment", asset_mapping["PTA"],
                                                index=asset_mapping["PTA"].index(st.session_state.equipment_name) if st.session_state.equipment_name in asset_mapping["PTA"] else 0)
                with edit_cols[1]:
                    edit_sub_equipment = st.selectbox("Sub-Equipment",
                                                    [comp for comp, _ in category_components["PTA"]],
                                                    index=[comp for comp, _ in category_components["PTA"]].index(st.session_state.sub_equipment) if st.session_state.sub_equipment in [comp for comp, _ in category_components["PTA"]] else 0)
                    sub_eq_problems = next((problems for sub_eq, problems in category_components["PTA"]
                                          if sub_eq == edit_sub_equipment), [])
                    problem_index = sub_eq_problems.index(st.session_state.problem_category) if st.session_state.problem_category in sub_eq_problems else 0
                    edit_category = st.selectbox("Problem Category", sub_eq_problems, index=problem_index)
                    em_index = em_categories.index(st.session_state.em_category) if st.session_state.em_category in em_categories else 0
                    edit_em_category = st.selectbox("E/M Category", em_categories, index=em_index)
                    edit_problem_desc = st.text_area("Problem Description", value=st.session_state.problem_description)
                    edit_action_taken = st.text_area("Action Taken", value=st.session_state.action_taken)
                with edit_cols[2]:
                    edit_permit_no = st.text_input("Permit No.", value=st.session_state.permit_no)
                    edit_spares = st.text_input("Spares Consumed", value=st.session_state.spares_consumed)
                    edit_remarks = st.text_input("Remarks", value=st.session_state.remarks)
                    edit_ic_name = st.text_input("I/C Name", value=st.session_state.ic_name)
                col_save, col_cancel = st.columns(2)
                with col_save:
                    save_edit = st.form_submit_button("💾 Save Changes")
                with col_cancel:
                    cancel_edit = st.form_submit_button("❌ Cancel Edit")
                if save_edit:
                    try:
                        dt_from = datetime.combine(edit_date, edit_start_time)
                        dt_to = datetime.combine(edit_date, edit_end_time)
                        if dt_to < dt_from:
                            dt_to = datetime.combine(edit_date + timedelta(days=1), edit_end_time)
                        duration = dt_to - dt_from
                        total_minutes = int(duration.total_seconds() // 60)
                        hours, minutes = divmod(total_minutes, 60)
                        duration_str = f"{hours:02d}:{minutes:02d}"
                    except:
                        duration_str = "00:00"
                    original_idx = df_filtered.index[st.session_state.edit_index]
                    st.session_state.df_existing.loc[original_idx, 'Date'] = pd.Timestamp(edit_date)
                    st.session_state.df_existing.loc[original_idx, 'Equipment'] = edit_equipment
                    st.session_state.df_existing.loc[original_idx, 'SubEquipment'] = edit_sub_equipment
                    st.session_state.df_existing.loc[original_idx, 'Problem'] = edit_category
                    st.session_state.df_existing.loc[original_idx, 'Category'] = "BD"
                    st.session_state.df_existing.loc[original_idx, 'EM_Category'] = edit_em_category
                    st.session_state.df_existing.loc[original_idx, 'Problem_Desc'] = edit_problem_desc
                    st.session_state.df_existing.loc[original_idx, 'Action'] = edit_action_taken
                    st.session_state.df_existing.loc[original_idx, 'Start_Time'] = edit_start_time
                    st.session_state.df_existing.loc[original_idx, 'End_Time'] = edit_end_time
                    st.session_state.df_existing.loc[original_idx, 'Duration'] = duration_str
                    st.session_state.df_existing.loc[original_idx, 'Permit'] = edit_permit_no
                    st.session_state.df_existing.loc[original_idx, 'Spares'] = edit_spares
                    st.session_state.df_existing.loc[original_idx, 'Remarks'] = edit_remarks
                    st.session_state.df_existing.loc[original_idx, 'IC'] = edit_ic_name
                    df_existing = st.session_state.df_existing
                    save_success = save_to_excel(df_existing)
                    if save_success:
                        st.success("✅ Entry updated successfully!")
                    else:
                        st.warning("Entry updated in session but there was an issue saving to Excel file.")
                    st.session_state.edit_mode = False
                    st.session_state.edit_index = None
                    st.rerun()
                if cancel_edit:
                    st.session_state.edit_mode = False
                    st.session_state.edit_index = None
                    st.rerun()

with tab2:
    st.markdown(
        "<div style='text-align: center; font-size:22px; font-weight: bold; color: blue;'>🚧 PTA Breakdown Analysis</div>",
        unsafe_allow_html=True
    )
    if st.button("🔄 Refresh Analytics Data"):
        current_session_data = st.session_state.df_existing.copy() if not st.session_state.df_existing.empty else pd.DataFrame()
        fresh_excel_data = load_data_from_excel()
        if not current_session_data.empty:
            st.session_state.df_existing = current_session_data
            st.success(f"✅ Keeping all {len(current_session_data)} session entries to prevent data loss.")
        else:
            st.session_state.df_existing = fresh_excel_data
            st.success("✅ Analytics data loaded from Excel file.")
        st.rerun()

    df = st.session_state.df_existing.copy()
    def convert_duration(x):
        try:
            if isinstance(x, str) and ':' in x:
                h, m = map(int, x.split(":"))
                return h + m/60
            return float(x)
        except:
            return np.nan

    if not df.empty:
        df['Duration_hr'] = df['Duration'].apply(convert_duration)
        df['Month'] = df['Date'].dt.to_period('M').astype(str)

    tabs = st.tabs(["📄 Raw Data", "📊 Analytics"])
    with tabs[0]:
        st.subheader("PTA Breakdown Entries")
        st.dataframe(df_existing, use_container_width=True)
    with tabs[1]:
        if not df.empty:
            col1, col2 = st.columns(2)
            with col1:
                st.markdown("### 📊 Equipment-wise Breakdown Frequency")
                equip_df = df.groupby('Equipment').size().reset_index(name='Breakdown Count')
                fig_equip = px.bar(equip_df, x="Equipment", y="Breakdown Count",
                                 color="Equipment", title="Breakdowns by Equipment")
                st.plotly_chart(fig_equip, use_container_width=True)
            with col2:
                st.markdown("### 🔧 Top Sub-Equipment Breakdowns")
                sub_equip_df = df.groupby('SubEquipment').size().reset_index(name='Count').sort_values('Count', ascending=False).head(10)
                fig_sub = px.bar(sub_equip_df, x="Count", y="SubEquipment",
                               orientation='h', title="Top 10 Sub-Equipment Breakdowns")
                st.plotly_chart(fig_sub, use_container_width=True)
            st.markdown("### 📈 Monthly Breakdown Trend")
            trend_df = df.groupby('Month').size().reset_index(name='Breakdown Count')
            fig_trend = px.line(trend_df, x='Month', y='Breakdown Count', markers=True,
                              title="Monthly Breakdown Trend")
            st.plotly_chart(fig_trend, use_container_width=True)
            col3, col4 = st.columns(2)
            with col3:
                st.markdown("### ⚡ Electrical vs Mechanical Breakdowns")
                category_df = df.groupby('EM_Category').size().reset_index(name='Count')
                fig_category = px.pie(category_df, values='Count', names='EM_Category',
                                    title="Breakdown Distribution by E/M Category")
                st.plotly_chart(fig_category, use_container_width=True)
            with col4:
                st.markdown("### ⏱️ Average Breakdown Duration")
                duration_df = df.groupby('Equipment')['Duration_hr'].mean().reset_index()
                duration_df = duration_df.sort_values('Duration_hr', ascending=False)
                fig_duration = px.bar(duration_df, x="Equipment", y="Duration_hr",
                                    title="Average Breakdown Duration (Hours)")
                st.plotly_chart(fig_duration, use_container_width=True)
            st.markdown("### 📊 MTTR Analysis by Equipment")
            if 'Duration_hr' in df.columns:
                mttr_df = df.groupby('Equipment')['Duration_hr'].agg(['mean', 'count', 'std']).reset_index()
                mttr_df.columns = ['Equipment', 'MTTR (Hours)', 'Breakdown Count', 'Std Dev']
                mttr_df = mttr_df.round(2)
                st.dataframe(mttr_df, use_container_width=True)
            st.markdown("### 🚨 Most Frequent Problems")
            problem_df = df.groupby('Problem_Desc').size().reset_index(name='Frequency').sort_values('Frequency', ascending=False).head(10)
            if not problem_df.empty:
                fig_problems = px.bar(problem_df, x="Frequency", y="Problem_Desc",
                                    orientation='h', title="Top 10 Most Frequent Problems")
                st.plotly_chart(fig_problems, use_container_width=True)
        else:
            st.info("No data available for analysis. Please add some breakdown entries first.")