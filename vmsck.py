import streamlit as st
import pandas as pd
from datetime import datetime, time
import os
import plotly.express as px
from collections import defaultdict
import numpy as np

# --- Page setup ---
st.set_page_config(layout="wide")

left_logo = r"C:\Users\<USER>\Downloads\images.jpg"  # Replace with your logo path or URL
right_logo =r"C:\Users\<USER>\Downloads\images.jpg"
right_logo1 =r"C:\Users\<USER>\Downloads\images.jpg"# Replace with your logo path or URL
col1, col2, col3 = st.columns([1, 6, 1])
with col1:
                st.image(left_logo, width=120)
with col2:
                st.markdown("<h1 style='text-align: center; font-size: 30px;'>Aditya Technological PTA Breakdown Analysis Application</h1>", unsafe_allow_html=True)
with col3:
                st.image(right_logo, width=120)



# --- Excel file for data source ---
EXCEL_FILE = "PTA BD ANALYSIS FY-21-22.xlsx"

# --- Load existing data from Excel ---
if os.path.exists(EXCEL_FILE):
    try:
        df_existing = pd.read_excel(EXCEL_FILE, sheet_name='PTA', header=1)
        # Clean up the data - keep only relevant columns
        relevant_columns = ['DATE', 'EQUIPMENT', 'SUB-EQUIPMENT', 'PROBLEM', 'CATEGORY', 'E/M',
                          'PROBLEM DESCRIPTION', 'ACTION TAKEN', 'START TIME', 'END TIME',
                          'TOTAL TIME', 'PERMIT NO.', 'SPARES CONSUMED', 'REMARKS', 'I/C']
        df_existing = df_existing[relevant_columns].dropna(subset=['DATE'])

        # Convert DATE to datetime if not already
        df_existing['DATE'] = pd.to_datetime(df_existing['DATE'])

    except Exception as e:
        st.error(f"Error reading Excel file: {e}")
        df_existing = pd.DataFrame(columns=relevant_columns)
else:
    df_existing = pd.DataFrame(columns=[
        'DATE', 'EQUIPMENT', 'SUB-EQUIPMENT', 'PROBLEM', 'CATEGORY', 'E/M',
        'PROBLEM DESCRIPTION', 'ACTION TAKEN', 'START TIME', 'END TIME',
        'TOTAL TIME', 'PERMIT NO.', 'SPARES CONSUMED', 'REMARKS', 'I/C'
    ])

def save_to_excel(df):
    # For now, we'll just display success message as we're reading from existing Excel
    # In a real implementation, you might want to append to a separate sheet or file
    pass

tab1,tab2=st.tabs(["e-Form Page","Analytics Overview"])

with tab1:
    st.markdown(
    "<div style='text-align: center; font-size:22px; font-weight: bold; color: blue;'>🚧 PTA Breakdown Analysis Log</div>",
    unsafe_allow_html=True)
    # --- PTA Equipment mapping based on Excel data ---
    asset_mapping = defaultdict(list)
    asset_pairs = [
        ("PTA", "PTA 1"), ("PTA", "PTA 2"), ("PTA", "PTA 3"), ("PTA", "PTA 4"), ("PTA", "PTA 5"),
        ("PTA", "PTA 6"), ("PTA", "PTA 7"), ("PTA", "PTA 8"), ("PTA", "PTA 9"), ("PTA", "PTA 10"),
        ("PTA", "PTA 11"), ("PTA", "PTA 12"), ("PTA", "PTA 13"), ("PTA", "PTA 14"), ("PTA", "PTA 15"),
        ("PTA", "PTA 16"), ("PTA", "PTA 17"), ("PTA", "PTA 18"), ("PTA", "PTA 19"), ("PTA", "PTA 20"),
    ]
    for cat, name in asset_pairs:
        asset_mapping[cat].append(name)

    # --- PTA Equipment → (Sub-Equipment, Problems) mapping based on Excel data ---
    category_components = {
        "PTA": [
            ("EXTRACTION", ["Mechanical", "Electrical"]),
            ("TAPPING HOIST", ["Mechanical", "Electrical"]),
            ("BATHPIPE", ["Mechanical", "Electrical"]),
            ("LT", ["Mechanical", "Electrical"]),
            ("CABIN", ["Mechanical", "Electrical"]),
            ("SHOVEL", ["Mechanical", "Electrical"]),
            ("ANODE HOIST", ["Mechanical", "Electrical"]),
            ("ANODE SETTING", ["Mechanical", "Electrical"]),
            ("CRUST BREAKER", ["Mechanical", "Electrical"]),
            ("METAL TAPPING", ["Mechanical", "Electrical"]),
            ("BATH TAPPING", ["Mechanical", "Electrical"]),
            ("ANODE CHANGING", ["Mechanical", "Electrical"]),
            ("FEEDING", ["Mechanical", "Electrical"]),
            ("ALUMINA FEEDING", ["Mechanical", "Electrical"]),
            ("CRYOLITE FEEDING", ["Mechanical", "Electrical"]),
            ("CARBON FEEDING", ["Mechanical", "Electrical"]),
            ("FLUORIDE FEEDING", ["Mechanical", "Electrical"]),
            ("CRANE", ["Mechanical", "Electrical"]),
            ("TROLLEY", ["Mechanical", "Electrical"]),
            ("HOIST", ["Mechanical", "Electrical"]),
            ("LONG TRAVEL", ["Mechanical", "Electrical"]),
            ("CROSS TRAVEL", ["Mechanical", "Electrical"]),
            ("GANTRY", ["Mechanical", "Electrical"]),
            ("BRIDGE", ["Mechanical", "Electrical"]),
            ("WINCH", ["Mechanical", "Electrical"]),
            ("MOTOR", ["Mechanical", "Electrical"]),
            ("GEARBOX", ["Mechanical", "Electrical"]),
            ("BRAKE", ["Mechanical", "Electrical"]),
            ("COUPLING", ["Mechanical", "Electrical"]),
            ("BEARING", ["Mechanical", "Electrical"]),
            ("ROPE", ["Mechanical", "Electrical"]),
            ("HOOK", ["Mechanical", "Electrical"]),
            ("BLOCK", ["Mechanical", "Electrical"]),
            ("WHEEL", ["Mechanical", "Electrical"]),
            ("RAIL", ["Mechanical", "Electrical"]),
            ("BUFFER", ["Mechanical", "Electrical"]),
            ("LIMIT SWITCH", ["Mechanical", "Electrical"]),
            ("CONTROL PANEL", ["Mechanical", "Electrical"]),
            ("CABLE", ["Mechanical", "Electrical"]),
            ("CONTACTOR", ["Mechanical", "Electrical"]),
        ],
    }


    # --- Initialize session_state keys ---
    form_keys = {
        "breakdown_date": datetime.today().date(),
        "start_time": time(10, 0),
        "end_time": time(12, 0),
        "equipment": "PTA",
        "equipment_name": asset_mapping["PTA"][0],
        "sub_equipment": "",
        "problem_category": "",
        "problem_description": "",
        "action_taken": "",
        "permit_no": "",
        "spares_consumed": "",
        "remarks": "",
        "ic_name": ""
    }

    for k, v in form_keys.items():
        if k not in st.session_state:
            st.session_state[k] = v

    pta_names = [
        "PTA 1", "PTA 2", "PTA 3", "PTA 4", "PTA 5", "PTA 6", "PTA 7", "PTA 8", "PTA 9", "PTA 10",
        "PTA 11", "PTA 12", "PTA 13", "PTA 14", "PTA 15", "PTA 16", "PTA 17", "PTA 18", "PTA 19", "PTA 20"
    ]

    # Define a list of visually distinct colors (loop will reuse if names > colors)
    colors = [
        "#1a73e8", "#d93025", "#188038", "#f9ab00", "#6f42c1", "#e37400", "#008080", "#ff6f61",
        "#4caf50", "#d81b60", "#5c6bc0", "#9c27b0", "#00897b", "#c2185b", "#607d8b", "#f57c00"
    ]

    # Create colored spans for each PTA
    pta_html = "".join([
        f"<span style='padding: 0 12px; color: {colors[i % len(colors)]};'>{name}</span>"
        for i, name in enumerate(pta_names)
    ])

    # Display the full styled row
    st.markdown(f"""
    <div style='display: flex; justify-content: center; flex-wrap: wrap; font-size: 16px; font-weight: bold; text-align: center;'>
        {pta_html}
    </div>
    """, unsafe_allow_html=True)

    # --- Create 4 columns for selectboxes ---
    col1, col2, col3, col4 = st.columns(4)

    # --- Equipment Category selectbox ---
    equipment_categories = sorted(asset_mapping.keys())
    with col1:
        selected_category = st.selectbox(
            "Equipment Category", equipment_categories,
            index=equipment_categories.index(st.session_state.equipment)
        )
    if selected_category != st.session_state.equipment:
        st.session_state.equipment = selected_category
        st.session_state.equipment_name = asset_mapping[selected_category][0]
        st.session_state.sub_equipment = ""
        st.session_state.problem_category = ""

    # --- Equipment Name selectbox ---
    filtered_names = asset_mapping[st.session_state.equipment]
    with col2:
        selected_name = st.selectbox(
            "Equipment Name", filtered_names,
            index=filtered_names.index(st.session_state.equipment_name)
        )
    if selected_name != st.session_state.equipment_name:
        st.session_state.equipment_name = selected_name

    # --- Sub-Equipment selectbox ---
    comp_pos_list = category_components.get(st.session_state.equipment, [])
    component_names = [comp for comp, _ in comp_pos_list]

    # Set default sub_equipment if not valid
    if st.session_state.sub_equipment not in component_names:
        st.session_state.sub_equipment = component_names[0] if component_names else ""

    with col3:
        sub_equipment_selection = st.selectbox(
            "Sub-Equipment",
            options=component_names,
            index=component_names.index(st.session_state.sub_equipment)
        )
    if sub_equipment_selection != st.session_state.sub_equipment:
        st.session_state.sub_equipment = sub_equipment_selection
        st.session_state.problem_category = ""

    # --- Problem Category selectbox ---
    category_options = next((pos_list for comp, pos_list in comp_pos_list if comp == st.session_state.sub_equipment), [])
    if not category_options:
        category_options = ["Mechanical", "Electrical"]

    if st.session_state.problem_category not in category_options:
        st.session_state.problem_category = category_options[0]

    with col4:
        category_selection = st.selectbox(
            "Problem Category (E/M)",
            options=category_options,
            index=category_options.index(st.session_state.problem_category)
        )
    if category_selection != st.session_state.problem_category:
        st.session_state.problem_category = category_selection


    st.markdown("---")
    st.markdown(
        "<div style='text-align: center; font-size:20px; font-weight: bold; color: red;'>🔧 Enter Breakdown Details Below</div>",
        unsafe_allow_html=True
    )

    # Show headers as a single row above the inputs:
    detail_headers = [
        "Breakdown Date", "Start Time", "End Time", "Total Time", "Problem Description",
        "Action Taken", "Permit No.", "Spares Consumed", "Remarks", "I/C Name"
    ]
    header_cols = st.columns(len(detail_headers))
    for col, header in zip(header_cols, detail_headers):
        col.markdown(f"**{header}**")

    # --- Form for the remaining fields ---

    from datetime import timedelta
    with st.form("breakdown_form"):
        input_cols = st.columns(10)

        st.session_state.breakdown_date = input_cols[0].date_input("Breakdown Date", value=st.session_state.breakdown_date, label_visibility="collapsed")

        st.session_state.start_time = input_cols[1].time_input(
            "Start Time",
            value=st.session_state.start_time,
            step=timedelta(minutes=5),
            label_visibility="collapsed"
        )

        st.session_state.end_time = input_cols[2].time_input(
            "End Time",
            value=st.session_state.end_time,
            step=timedelta(minutes=5),
            label_visibility="collapsed"
        )














        # Duration calculation
        try:
            dt_from = datetime.combine(st.session_state.breakdown_date, st.session_state.start_time)
            dt_to = datetime.combine(st.session_state.breakdown_date, st.session_state.end_time)

            if dt_to < dt_from:
                # Handle case where end time is next day
                dt_to = datetime.combine(st.session_state.breakdown_date + timedelta(days=1), st.session_state.end_time)

            duration = dt_to - dt_from
            total_minutes = int(duration.total_seconds() // 60)
            hours, minutes = divmod(total_minutes, 60)
            duration_str = f"{hours:02d}:{minutes:02d}"
        except Exception:
            duration_str = "Invalid"

        input_cols[3].text_input("Total Time", value=duration_str, disabled=True, label_visibility="collapsed")

        st.session_state.problem_description = input_cols[4].text_input("Problem Description", value=st.session_state.problem_description, label_visibility="collapsed")
        st.session_state.action_taken = input_cols[5].text_input("Action Taken", value=st.session_state.action_taken, label_visibility="collapsed")
        st.session_state.permit_no = input_cols[6].text_input("Permit No.", value=st.session_state.permit_no, label_visibility="collapsed")
        st.session_state.spares_consumed = input_cols[7].text_input("Spares Consumed", value=st.session_state.spares_consumed, label_visibility="collapsed")
        st.session_state.remarks = input_cols[8].text_input("Remarks", value=st.session_state.remarks, label_visibility="collapsed")
        st.session_state.ic_name = input_cols[9].text_input("I/C Name", value=st.session_state.ic_name, label_visibility="collapsed")

        submitted = st.form_submit_button("Submit Breakdown Entry")

        if submitted:
            # Basic validation
            if not st.session_state.problem_description.strip():
                st.error("❌ Problem Description is required.")
            else:
                new_entry = pd.DataFrame([{
                    "DATE": st.session_state.breakdown_date,
                    "EQUIPMENT": st.session_state.equipment_name,
                    "SUB-EQUIPMENT": st.session_state.sub_equipment,
                    "PROBLEM": st.session_state.problem_description,
                    "CATEGORY": st.session_state.problem_category,
                    "E/M": st.session_state.problem_category,
                    "PROBLEM DESCRIPTION": st.session_state.problem_description,
                    "ACTION TAKEN": st.session_state.action_taken,
                    "START TIME": st.session_state.start_time,
                    "END TIME": st.session_state.end_time,
                    "TOTAL TIME": duration_str,
                    "PERMIT NO.": st.session_state.permit_no,
                    "SPARES CONSUMED": st.session_state.spares_consumed,
                    "REMARKS": st.session_state.remarks,
                    "I/C": st.session_state.ic_name
                }])
                df_existing = pd.concat([df_existing, new_entry], ignore_index=True)
                save_to_excel(df_existing)
                st.success("✅ Breakdown entry saved successfully!")

                # Reset form fields
                st.session_state.breakdown_date = datetime.today().date()
                st.session_state.start_time = time(10, 0)
                st.session_state.end_time = time(12, 0)
                st.session_state.problem_description = ""
                st.session_state.action_taken = ""
                st.session_state.permit_no = ""
                st.session_state.spares_consumed = ""
                st.session_state.remarks = ""
                st.session_state.ic_name = ""

    # --- Show saved entries ---
    if not df_existing.empty:
        st.markdown(
            "<div style='text-align: center; font-size:20px; font-weight: bold; color: green;'>📋 PTA Breakdown Entries</div>",
            unsafe_allow_html=True
        )

        with st.expander("🔍 Filter Entries"):
            # Date range filter
            min_date = df_existing['DATE'].min()
            max_date = df_existing['DATE'].max()
            start_date, end_date = st.date_input("Date Range", (min_date.date(), max_date.date()))

            # Filter Equipment
            equipment_list = ["All"] + sorted(df_existing['EQUIPMENT'].dropna().unique())
            selected_equipment = st.selectbox("Equipment", equipment_list)

            # Filter Sub-Equipment based on selected equipment
            if selected_equipment != "All":
                filtered_sub_equipment = df_existing[df_existing['EQUIPMENT'] == selected_equipment]['SUB-EQUIPMENT'].dropna().unique()
            else:
                filtered_sub_equipment = df_existing['SUB-EQUIPMENT'].dropna().unique()
            sub_equipment_list = ["All"] + sorted(filtered_sub_equipment)
            selected_sub_equipment = st.selectbox("Sub-Equipment", sub_equipment_list)

            # Filter Problem Category
            category_list = ["All"] + sorted(df_existing['CATEGORY'].dropna().unique())
            selected_category = st.selectbox("Problem Category", category_list)

            # Apply All Filters
            df_filtered = df_existing[
                (df_existing['DATE'].dt.date >= start_date) &
                (df_existing['DATE'].dt.date <= end_date)
            ]

            if selected_equipment != "All":
                df_filtered = df_filtered[df_filtered['EQUIPMENT'] == selected_equipment]

            if selected_sub_equipment != "All":
                df_filtered = df_filtered[df_filtered['SUB-EQUIPMENT'] == selected_sub_equipment]

            if selected_category != "All":
                df_filtered = df_filtered[df_filtered['CATEGORY'] == selected_category]

        # Sort by date
        df_display = df_filtered.sort_values(by="DATE", ascending=False)



        custom_css = """
<style>
.custom-table {
    border-collapse: collapse;
    width: 100%;
    font-family: Arial, sans-serif;
}
.custom-table th {
    background-color: #003366;
    color: white;
    font-weight: bold;
    padding: 8px;
    text-align: center;
}
.custom-table td {
    border: 1px solid #ddd;
    padding: 8px;
    text-align: center;
}
</style>
"""

# Convert DataFrame to HTML with the custom class
        html_table = df_display.to_html(index=False, classes="custom-table")
    
    # Display styled table
        st.markdown(custom_css + html_table, unsafe_allow_html=True)


        #st.dataframe(df_display, use_container_width=True)
        
        import win32com.client
        import pythoncom
        
        def send_email(df, subject, recipients):
            pythoncom.CoInitialize()
            df_html = df.to_html(index=False)
            outlook = win32com.client.Dispatch('outlook.application')
            mail = outlook.CreateItem(0)
            mail.Subject = subject
            mail.To = "; ".join(recipients)
            mail.HTMLBody = f"""
            <p>Hello Sir,</p>
            <p>Please find below the List of maintenance related entries in vehicles:</p>
            {df_html}
            <p>Best regards,</p>
            <p>Team Digital-Aditya Smelter</p>
            """
            mail.Send()
            pythoncom.CoUninitialize()
            print("Email sent successfully.")
        
        if st.button("Mail the Report") and not df_display.empty:
            subject = "Vehicle Maintenance: Maintenance Logger History card"
            recipients = ['<EMAIL>','<EMAIL>','<EMAIL>','<EMAIL>']
            send_email(df_display, subject, recipients)

with tab2:
     st.markdown(
    "<div style='text-align: center; font-size:22px; font-weight: bold; color: blue;'>🚧 PTA Breakdown Analysis</div>",
    unsafe_allow_html=True)

     df = df_existing.copy()

     def convert_duration(x):
        try:
            if isinstance(x, str) and ':' in x:
                h, m = map(int, x.split(":"))
                return h + m/60
            return float(x)
        except:
            return np.nan

     if not df.empty:
         df['Duration_hr'] = df['TOTAL TIME'].apply(convert_duration)
         df['Month'] = df['DATE'].dt.to_period('M').astype(str)

     tabs = st.tabs(["📄 Raw Data", "📊 Analytics"])

    # ============================ Tab 1: Raw Data
     with tabs[0]:
        st.subheader("PTA Breakdown Entries")
        st.dataframe(df_existing, use_container_width=True)

    # ============================ Tab 2: Analytics
     with tabs[1]:
        if not df.empty:
            col1, col2 = st.columns(2)

            # 1️⃣ Equipment-wise Breakdown Frequency
            with col1:
                st.markdown("### 📊 Equipment-wise Breakdown Frequency")
                equip_df = df.groupby('EQUIPMENT').size().reset_index(name='Breakdown Count')
                fig_equip = px.bar(equip_df, x="EQUIPMENT", y="Breakdown Count",
                                 color="EQUIPMENT", title="Breakdowns by Equipment")
                st.plotly_chart(fig_equip, use_container_width=True)

            # 2️⃣ Sub-Equipment Analysis
            with col2:
                st.markdown("### � Top Sub-Equipment Breakdowns")
                sub_equip_df = df.groupby('SUB-EQUIPMENT').size().reset_index(name='Count').sort_values('Count', ascending=False).head(10)
                fig_sub = px.bar(sub_equip_df, x="Count", y="SUB-EQUIPMENT",
                               orientation='h', title="Top 10 Sub-Equipment Breakdowns")
                st.plotly_chart(fig_sub, use_container_width=True)

            # 3️⃣ Monthly Breakdown Trend
            st.markdown("### 📈 Monthly Breakdown Trend")
            trend_df = df.groupby('Month').size().reset_index(name='Breakdown Count')
            fig_trend = px.line(trend_df, x='Month', y='Breakdown Count', markers=True,
                              title="Monthly Breakdown Trend")
            st.plotly_chart(fig_trend, use_container_width=True)

            # 4️⃣ Problem Category Analysis (E/M)
            col3, col4 = st.columns(2)
            with col3:
                st.markdown("### ⚡ Electrical vs Mechanical Breakdowns")
                category_df = df.groupby('CATEGORY').size().reset_index(name='Count')
                fig_category = px.pie(category_df, values='Count', names='CATEGORY',
                                    title="Breakdown Distribution by Category")
                st.plotly_chart(fig_category, use_container_width=True)

            # 5️⃣ Average Breakdown Duration by Equipment
            with col4:
                st.markdown("### ⏱️ Average Breakdown Duration")
                duration_df = df.groupby('EQUIPMENT')['Duration_hr'].mean().reset_index()
                duration_df = duration_df.sort_values('Duration_hr', ascending=False)
                fig_duration = px.bar(duration_df, x="EQUIPMENT", y="Duration_hr",
                                    title="Average Breakdown Duration (Hours)")
                st.plotly_chart(fig_duration, use_container_width=True)
        else:
            st.info("No data available for analysis. Please add some breakdown entries first.")