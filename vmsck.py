import streamlit as st
import pandas as pd
from datetime import datetime, time
import os
import plotly.express as px
from collections import defaultdict

# --- Page setup ---
st.set_page_config(layout="wide")

left_logo = r"C:\Users\<USER>\Downloads\images.jpg"  # Replace with your logo path or URL
right_logo =r"C:\Users\<USER>\Downloads\images.jpg" 
right_logo1 =r"C:\Users\<USER>\Downloads\images.jpg"# Replace with your logo path or URL
col1, col2, col3 = st.columns([1, 6, 1])
with col1: 
                st.image(left_logo, width=120)
with col2:
                st.markdown("<h1 style='text-align: center; font-size: 30px;'>Aditya Technolgical Vehicle Maintenance Tracker Application</h1>", unsafe_allow_html=True)
with col3: 
                st.image(right_logo, width=120)



# --- CSV file for persistence ---
CSV_FILE = "submitted_entries.csv"

# --- Load existing data ---
if os.path.exists(CSV_FILE):
    df_existing = pd.read_csv(CSV_FILE, parse_dates=["Date From", "Date To"])
else:
    df_existing = pd.DataFrame(columns=[
        "Date From", "Date To", "Time From", "Time To", "Duration", "Failure HMR",
        "Asset Category", "Asset Name", "Failure/Scheduled", "Position", "Old/New",
        "Previous HMR", "Life (hrs)", "Remarks", "SI Name"
    ])

def save_to_csv(df):
    df.to_csv(CSV_FILE, index=False)

tab1,tab2=st.tabs(["e-Form Page","Analytics Overview"])

with tab1:
    st.markdown(
    "<div style='text-align: center; font-size:22px; font-weight: bold; color: blue;'>🚧 Asset Failure / Replacement Log</div>",
    unsafe_allow_html=True)
    # --- Asset Category → Asset Name mapping ---
    asset_mapping = defaultdict(list)
    asset_pairs = [
        ("ATV", "ATV-01"), ("ATV", "ATV-02"), ("ATV", "ATV-03"), ("ATV", "ATV-04"), ("ATV", "ATV-05"), ("ATV", "ATV-06"),
        ("MTV", "MTV-01"), ("MTV", "MTV-02"), ("MTV", "MTV-03"), ("MTV", "MTV-04"), ("MTV", "MTV-05"),
        ("BTV", "BTV-01"), ("BTV", "BTV-02"),
        ("FTV", "FTV-01"), ("FTV", "FTV-02"),
        ("THTV", "THTV-01"), ("THTV", "THTV-02"),
        ("CBT", "CBT-01"), ("CBT", "CBT-02"), ("CBT", "CBT-03"), ("CBT", "CBT-04"),
        *[(f"FLT", f"FLT-{str(i).zfill(2)}") for i in range(1, 27)],
        ("FLT-EV", "FLT EV-01"), ("FLT-EV", "FLT EV-02"), ("FLT-EV", "FLT EV-03"),
        ("RSV", "RSV-01"), ("IVC", "IVC-01"),
        ("FF", "FF-01"), ("FF", "FF-02"), ("FF", "FF-03"),
        ("Manlift", "Manlift-01"), ("Manlift", "Manlift-02"),
        ("Minidozer", "Minidozer-01"), ("Minidozer", "Minidozer-02"),
        ("Tractor", "Tractor-01"),
        *[(f"BC", f"BC-{str(i).zfill(2)}") for i in range(1, 25)],
        ("Ambulance", "Ambulance-01"), ("Ambulance", "Ambulance-02"), ("Ambulance", "Ambulance-03"),
    ]
    for cat, name in asset_pairs:
        asset_mapping[cat].append(name)

    # --- Asset Category → (Component, Positions) mapping (Only ATV example given) ---
    category_components = {
        "ATV": [
            ("Boggie assembly", ["LHS", "RHS"]),
            ("Articulation joint assembly", ["Articulation joint"]),
            ("Engine", ["Engine"]),
            ("Radiator", ["Engine"]),
            ("Alternator", ["Engine"]),
            ("Self-starter", ["Engine"]),
            ("Air compressor", ["Engine"]),
            ("AC compressor", ["Engine"]),
            ("Hydraulic main pump", ["Engine"]),
            ("Hydraulic drive motor", ["LHS", "RHS"]),
            ("Wheel reducer gearbox", ["LHS", "RHS"]),
            ("Steering cylinder", ["LHS", "RHS"]),
            ("Pallet lifting cylinder", ["Left In", "Left Out", "Right In", "Right Out"]),
            ("Rain hood cylinder", ["Left In", "Left Out", "Right In", "Right Out"]),
            ("Dual gear pump", ["Main Pump"]),
            ("Hydraulic hose set", ["Hydraulic system"]),
            ("Counter balancing valve", ["Rear of cabin"]),
            ("Steering Unit", ["Lower of cabin"]),
            ("Centerflex coupling", ["Between Pump & engine"]),
            ("DC valve - Parking", ["Lower of cabin"]),
            ("DC valve - High/Low speed", ["Lower of cabin"]),
            ("DC valve - Pallet & steering", ["Lower of cabin"]),
            ("DC valve - Pallet up/down", ["Rear of cabin"]),
            ("DC valve - Rain hood up/down", ["Rear of cabin"]),
            ("Pressure relief valve - Pallet up/down & steering", ["Lower of cabin"]),
            ("Oil Cooler", ["Radiator"]),
            ("Hydraulic block - Parking brake", ["Lower of cabin"]),
            ("Hydraulic block - High/low speed", ["Lower of cabin"]),
            ("Hydraulic block - Pallet & steering", ["Lower of cabin"]),
            ("Hydraulic block - Pallet up/down", ["Rear of cabin"]),
            ("Hydraulic block - Flow divider", ["Rear of cabin"]),
            ("DC valve - Main pump", ["Main pump"]),
            ("Insert", ["Engine"]),
            ("Operator cabin", ["Front"]),
            ("Battery", ["Right side of cabin"]),
            ("Charging pump", ["Main Pump"]),
            ("DA Valve", ["Main Pump"]),
            ("Radiator Fan", ["Engine"]),
            ("Operator seat", ["Cabin"]),
            ("Rear view camera", ["Rear"]),
            ("Master Cylinder", ["Rear of cabin"]),
        ],
        "MTV": [
            ("Steering Motor", ["LHS", "RHS"]),
            ("Drive Motor", ["Differential"]),
            ("Negative brake assembly", ["Differential"]),
            ("Rotary Joint", ["Slew bearing"]),
            ("Differential assembly", ["Lower of cabin"]),
            ("Hydraulic main pump", ["Engine"]),
            ("PTO pump", ["Main pump"]),
            ("Gear pump", ["PTO pump"]),
            ("Hydraulic oil cooler", ["Rear of cabin"]),
            ("Platform lifting cylinder", ["LHS", "RHS"]),
            ("Lid cylinder", ["LHS", "RHS"]),
            ("Cabin tilting cylinder", ["Cabin center"]),
            ("Steering unit", ["Lower of cabin"]),
            ("Brake padle valve", ["Lower of cabin"]),
            ("Slew bearing", ["Lower of cabin"]),
            ("Priority valve", ["Rear of cabin"]),
            ("Preassure releief valve", ["Oil cooler"]),
            ("DC valve", ["Parking brake"]),
            ("Counter balancing valve", ["Platform lifting cylinder-LHS", "Platform lifting cylinder-RHS", "Lid cylinder - Center", "Cabin tilting cylinder"]),
            ("Control valve", ["Near oil cooler"]),
            ("Hydraulic hose set", ["Hydraulic system"]),
            ("Engine", ["Engine"]),
            ("Alternator", ["Engine"]),
            ("Self-starter", ["Engine"]),
            ("Centerflex coupling", ["Between Pump & engine"]),
            ("Platform", ["Chassis"]),
            ("Center axle", ["Middle"]),
            ("Drive axle", ["Front", "Rear"]),
            ("Tie rod", ["LHS", "RHS"]),
            ("Oil cooler", ["Rear of cabin"]),
            ("Radiator", ["Engine"]),
            ("Hydraulic block - Parking brake", ["Lower of return hydraulic tank"]),
            ("DC valve - Main pump", ["Main pump"]),
            ("Insert", ["Engine"]),
            ("Negative brake clutch", ["Negative brake assembly"]),
            ("Battery", ["Right side of cabin"]),
            ("Operator cabin", ["Front"]),
            ("Charging pump", ["Main Pump"]),
            ("DA Valve", ["Main Pump"]),
            ("Radiator Fan", ["Engine"]),
            ("Leaf spring", ["LHS", "RHS"]),
            ("Drive Coupling - Male", ["Differential"]),
            ("Drive Coupling - Female", ["Differential"]),
            ("Operator seat", ["Cabin"]),
            ("Accelerator paddle", ["Inside cabin"]),
            ("Rear view camera", ["Rear"]),
        ],
        "BTV": [
            ("Engine", ["Engine"]),
            ("Radiator", ["Engine"]),
            ("Engine coupling", ["Engine"]),
            ("Covertor", ["Cabin"]),
            ("Alternator", ["Engine"]),
            ("Self-starter", ["Engine"]),
            ("AC compressor", ["Engine"]),
            ("Crane arm - Motor side", ["Crane unit"]),
            ("Crane arm - Lock/unlock side", ["Crane unit"]),
            ("Crane column", ["Crane unit"]),
            ("Jib crane assembly", ["Crane unit"]),
            ("Steering unit", ["Lower of cabin"]),
            ("Drive Motor", ["Differential"]),
            ("Negative brake assembly", ["Differential"]),
            ("Differential assembly", ["Lower of cabin"]),
            ("Hydraulic main pump", ["Engine"]),
            ("PTO pump", ["Engine"]),
            ("Gear pump", ["PTO pump"]),
            ("Compressor pump", ["Engine"]),
            ("Hydraulic oil cooler", ["LHS", "RHS"]),
            ("Compressor Motor", ["Compressor"]),
            ("Control valve", ["Left side of cabin"]),
            ("Brake valve", ["Lower of cabin"]),
            ("Accumulator", ["Lower of cabin"]),
            ("Stabilizer cylinder", ["Front"]),
            ("Stabilizer cylinder", ["Rear"]),
            ("Laddle Up/down Cylinder", ["Crane unit"]),
            ("Lock/unlock cylinder - Arm Unit", ["LHS", "RHS"]),
            ("Steering Cylinder", ["Steering axle"]),
            ("Suspenssion cylinder", ["LHS", "RHS"]),
            ("Launder cylinder-Tilting", ["LHS", "RHS"]),
            ("Launder cylinder-Up/Down", ["LHS", "RHS"]),
            ("Steering Axle", ["Rear"]),
            ("Compressor", ["Rear right"]),
            ("Emergency pump - Electrical", ["Top of hydraulic tank"]),
            ("Emergency pump - Manual", ["Rear of hydraulic tank"]),
            ("Saddle cylinder", ["Crane unit"]),
            ("Bearing - laddle tilting", ["Crane unit"]),
            ("Bearing - laddle rotating", ["Crane unit"]),
            ("Bearing - Laddle in-out", ["Crane unit"]),
            ("Spincer", ["Front left", "Front right", "Rear left", "Rear right"]),
            ("Reductional gearbox", ["Engine"]),
            ("Coupler", ["Engine"]),
            ("AC Unit", ["Cabin top"]),
            ("Bearing - Crane sliding", ["Crane unit"]),
            ("Bearing - Crane up/down", ["Crane unit"]),
            ("DC valve nagative brake", ["Lower of hydraulic tank"]),
            ("DC valve block", ["Lower of hydraulic tank"]),
            ("Pressure relief valve - Stabilizer cylinder", ["Lower of hydraulic tank"]),
            ("Battery", ["Lower of compressure"]),
            ("Hydraulic hose set", ["Hydraulic system"]),
            ("Priority valve", ["Top of reductinal gearbox"]),
            ("Operator cabin", ["Front"]),
            ("Charging pump", ["Main Pump", "Compressor Pump"]),
            ("Rail assembly", ["Crane unit"]),
            ("Radiator Fan", ["Engine"]),
            ("Oil cooler fan", ["Oil cooler"]),
            ("Motor - Laddle tilting", ["Crane unit"]),
            ("Motor - Laddle rotating", ["Crane unit"]),
            ("Motor - Laddle in/out", ["Crane unit"]),
            ("King Pin", ["LHS", "RHS"]),
            ("Leaf spring", ["LHS", "RHS"]),
            ("Drive Coupling - Male", ["Differential"]),
            ("Drive Coupling - Female", ["Differential"]),
            ("Operator seat", ["Cabin"]),
            ("Accelerator paddle", ["Inside cabin"]),
            ("Camera", ["Launder"]),
            ("Rear view camera", ["Rear"]),
            ("Monitor", ["Front", "Rear"]),
        ],
        "RSV": [
            ("Engine", ["Front", "Rear"]),
            ("Radiator", ["Front Engine", "Rear Engine"]),
            ("Alternator", ["Front Engine", "Rear Engine"]),
            ("Self-starter", ["Front Engine", "Rear Engine"]),
            ("Center brush", ["Middle lower"]),
            ("Side Brush", ["LSH", "RHS"]),
            ("Gearbox", ["Gearbox"]),
            ("Clutch plate", ["Gearbox"]),
            ("Pressure plate", ["Gearbox"]),
            ("Release bearing", ["Gearbox"]),
            ("Radiator Fan", ["Engine"]),
            ("Operator seat", ["Cabin"]),
            ("Battery", ["Engine"]),
        ],
        "Manlift": [
            ("Engine", ["Engine"]),
            ("Radiator", ["Engine"]),
            ("Alternator", ["Engine"]),
            ("Gearbox", ["Gearbox"]),
            ("Clutch plate", ["Gearbox"]),
            ("Pressure plate", ["Gearbox"]),
            ("Release bearing", ["Gearbox"]),
            ("Radiator Fan", ["Engine"]),
            ("Operator seat", ["Cabin"]),
            ("Hydraulic hose set", ["Hydraulic System"]),
            ("Self-starter", ["Engine"]),
            ("Differential", ["Rear"]),
            ("Leaf Spring", ["Front", "Rear"]),
            ("Battery", ["Engine"]),
        ],
        "CBT": [
            ("Engine", ["Engine"]),
            ("Radiator", ["Engine"]),
            ("Alternator", ["Engine"]),
            ("Gearbox", ["Gearbox"]),
            ("Clutch plate", ["Gearbox"]),
            ("Pressure plate", ["Gearbox"]),
            ("Release bearing", ["Gearbox"]),
            ("Operator seat", ["Cabin"]),
            ("Self-starter", ["Engine"]),
            ("Turn-table King Pin", ["Turn-table"]),
            ("Ball Valve", ["Tanker"]),
            ("Differential", ["Rear"]),
            ("AC Compressor", ["Engine"]),
            ("Air Compressor", ["Engine"]),
            ("Leaf Spring", ["Front", "Rear"]),
            ("Battery", ["Engine"]),
        ],
        "FTV": [
            ("Main Pump", ["Engine"]),
            ("PTO Pump", ["Main pump"]),
            ("Gear Pump", ["Engine"]),
            ("Control Valve", ["Rear"]),
            ("Coupling", ["Engine"]),
            ("Boom UP/Down cylinder", ["LHS", "RHS"]),
            ("Internal telescope cylinder", ["Boom"]),
            ("Tilting cylinder", ["Boom"]),
            ("Cabin up/down cylinder", ["Cabin"]),
            ("Cylinder - Charging box lock/unlock", ["Charging box"]),
            ("Cylinder - Fork lock/unlock cylinder", ["Boom"]),
            ("Rotary joint", ["Rear"]),
            ("Drive motor", ["LHS", "RHS"]),
            ("Slew bearing", ["Rear"]),
            ("Flow divider block", ["Rear"]),
            ("Hose real", ["Side of cabin"]),
            ("Flow divider", ["Hose real"]),
            ("Boom Motor", ["LHS", "RHS"]),
            ("Engine", ["Engine"]),
            ("Radiator", ["Engine"]),
            ("Alternator", ["Engine"]),
            ("Radiator Fan", ["Engine"]),
            ("Steering Motor", ["Sleaw bearing"]),
            ("Accumulator", ["Lower of cabin"]),
            ("Steering Unit", ["Lower of cabin"]),
            ("Accelerator padle", ["Inside Cabin"]),
            ("Operator seat", ["Cabin"]),
            ("Self-starter", ["Engine"]),
            ("Hydraulic hose set", ["Hydraulic System"]),
        ],
        "THTV": [
            ("Main pump", ["Engine"]),
            ("Compressor Pump", ["Engine"]),
            ("PTO Pump", ["Compressor pump"]),
            ("Gear Pump", ["Engine"]),
            ("Compressor Motor", ["Compressor"]),
            ("Oil cooler Motor", ["Oil cooler"]),
            ("Air compressor", ["Rear"]),
            ("Drive Motor", ["Differential"]),
            ("Centerflex coupling", ["Engine"]),
            ("Oil cooler", ["Radiator"]),
            ("Steering Unit", ["Lower of cabin"]),
            ("Control Valve", ["RHS"]),
            ("Priority valve", ["Rear of cabin"]),
            ("Accumulator", ["Rear of cabin"]),
            ("Reductional gearbox", ["Engine"]),
            ("Differential", ["Rear"]),
            ("Steering cylinder", ["Front"]),
            ("Steering Axle", ["Front"]),
            ("Brake Valve", ["Lower of cabin"]),
            ("Cylinder - Boom UP/Down", ["LHS", "RHS"]),
            ("Cylinder - Internal Boom", ["Internal boom"]),
            ("Cylinder - Discharge pipe", ["Rear of cabin"]),
            ("Cylinder rotating - discharge pipe", ["Rear of cabin"]),
            ("Cylinder - Butterfly valve", ["Rear of cabin"]),
            ("Cylinder - hatch cover sliding", ["LHS", "RHS"]),
            ("Cylinder - Cabin tilting", ["Lower of cabin"]),
            ("Operator seat", ["Cabin"]),
            ("Drum roller", ["Drum"]),
            ("Drum Motor", ["Drum"]),
            ("Drum gearbox", ["Drum"]),
            ("Flow divider block", ["Compressor"]),
            ("Suspenssion pad", ["Differential"]),
            ("Oil cooler", ["Compressor"]),
            ("Potentiometer", ["Inside cabin"]),
            ("Engine", ["Engine"]),
            ("Radiator", ["Engine"]),
            ("Alternator", ["Engine"]),
            ("Radiator Fan", ["Engine"]),
            ("Self-starter", ["Engine"]),
            ("Hydraulic hose set", ["Hydraulic System"]),
            ("Battery", ["Engine"]),
        ],
        "FLT": [
            ("Engine", ["Engine"]),
            ("Radiator", ["Engine"]),
            ("Alternator", ["Engine"]),
            ("Radiator Fan", ["Engine"]),
            ("Operator Seat", ["Operator Seat"]),
            ("Differential assembly", ["Front"]),
            ("Drive axle", ["Front"]),
            ("Self-starter", ["Engine"]),
            ("Propeller Shaft", ["Engine"]),
            ("Hydraulic Pump", ["Hydraulic System"]),
            ("Control Valve", ["Hydraulic System"]),
            ("Steering unit", ["Hydraulic System"]),
            ("Hydraulic hose set", ["Hydraulic System"]),
            ("HVM assembly", ["Front"]),
            ("Hoist cylinder", ["LHS", "RHS"]),
            ("Tilt Cylinder", ["LHS", "RHS"]),
            ("Battery", ["Engine"]),
            ("Carriage", ["Mast"]),
            ("Fork", ["Front"]),
            ("Trial axle assembly", ["Rear"]),
            ("Rear view camera", ["Rear"]),
            ("Front view camera", ["Front"]),
            ("Hood", ["Seat"]),
            ("Steering cylinder", ["Rear"]),
            ("Transmission gearbox", ["Engine"]),
            ("Rotating unit", ["Front"]),
            ("Brake shoe set", ["Front wheel"]),
            ("Master Cylinder", ["Front wheel"]),
        ],
        "Minidozer": [
            ("Track kit Set", ["Track"]),
            ("Battery", ["Rear"]),
            ("Wheel bearing", ["Wheel"]),
            ("Hydraulic oil", ["Hydraulic Tank"]),
            ("Hydraulic filter", ["Hydraulic Tank"]),
            ("Drive Motor", ["LHS", "RHS"]),
            ("Hydraulic pump", ["Hydraulic Tank"]),
            ("Tilt cylinder", ["Front"]),
            ("Hoist cylinder", ["Front"]),
            ("Controller", ["Battery"]),
            ("Converter", ["Battery"]),
        ],
        "FF": [  # FIRE TENDER
            ("Engine", ["Engine"]),
            ("Radiator", ["Engine"]),
            ("Alternator", ["Engine"]),
            ("Gearbox", ["Gearbox"]),
            ("Clutch plate", ["Gearbox"]),
            ("Pressure plate", ["Gearbox"]),
            ("Release bearing", ["Gearbox"]),
            ("Operator seat", ["Cabin"]),
            ("Self-starter", ["Engine"]),
            ("Differential", ["Rear"]),
            ("Leaf Spring", ["Front", "Rear"]),
            ("Battery", ["Engine"]),
        ],
        "IVC": [
            ("Engine", ["Engine"]),
            ("Radiator", ["Engine"]),
            ("Alternator", ["Engine"]),
            ("Gearbox", ["Gearbox"]),
            ("Clutch plate", ["Gearbox"]),
            ("Pressure plate", ["Gearbox"]),
            ("Release bearing", ["Gearbox"]),
            ("Operator seat", ["Cabin"]),
            ("Self-starter", ["Engine"]),
            ("Differential", ["Rear"]),
            ("Leaf Spring", ["Front", "Rear"]),
            ("Battery", ["Engine"]),
        ],
        "BC": [
            ("Battery", ["Electrical syatem"]),
            ("Seat", ["Front", "Rear"]),
            ("Seat belt", ["Front", "Rear"]),
            ("Potentiometer", ["Front"]),
        ],
        "Tractor": [
            ("Engine", ["Engine"]),
            ("Radiator", ["Engine"]),
            ("Alternator", ["Engine"]),
            ("Radiator Fan", ["Engine"]),
            ("Operator Seat", ["Operator Seat"]),
            ("Gearbox", ["Engine"]),
            ("Battery", ["Engine"]),
            ("Self-starter", ["Engine"]),
        ],
        "Ambulance": [
            ("Engine", ["Engine"]),
            ("Radiator", ["Engine"]),
            ("Alternator", ["Engine"]),
            ("Radiator Fan", ["Engine"]),
            ("Operator Seat", ["Operator Seat"]),
            ("Gearbox", ["Engine"]),
            ("Battery", ["Engine"]),
            ("Self-starter", ["Engine"]),
        ],
        "FLT-EV": [
        ("Battery", ["Middle of vehicle"]),
        ("Electric Motor", ["Marching Operation", "Fork Operation"]),
        ("Accelerator Paddle", ["Operator"]),
        ("Controller", ["Marching Operation Motor", "Fork Operation Motor"]),
        ("Reverse Motion Sensor", ["Rear"]),
        ("Operator Seat", ["Operator Seat"]),
    ],
    }

    # --- Initialize session_state keys ---
    form_keys = {
        "date_from": datetime.today().date(),
        "date_to": datetime.today().date(),
        "time_from": time(10, 0),
        "time_to": time(12, 0),
        "failure_hmr": "",
        "asset_category": "ATV",
        "asset_name": asset_mapping["ATV"][0],
        "spare_type": "",
        "position_spare": "",
        "old_new": "New",
        "prev_occ_hmr": "",
        "remarks": "",
        "si_name": ""
    }

    for k, v in form_keys.items():
        if k not in st.session_state:
            st.session_state[k] = v

    vehicle_names = [
    "ATV", "MTV", "BTV", "RSV", "Manlift", "CBT", "FTV", "THTV",
    "Forklift", "MINIDOZER", "FF", "IVC", "BATTERY CAR", "TRACTOR", "AMBULANCE", "FLT-EV"
    ]

    # Define a list of visually distinct colors (loop will reuse if names > colors)
    colors = [
        "#1a73e8", "#d93025", "#188038", "#f9ab00", "#6f42c1", "#e37400", "#008080", "#ff6f61",
        "#4caf50", "#d81b60", "#5c6bc0", "#9c27b0", "#00897b", "#c2185b", "#607d8b", "#f57c00"
    ]

    # Create colored spans for each vehicle
    vehicle_html = "".join([
        f"<span style='padding: 0 12px; color: {colors[i % len(colors)]};'>{name}</span>"
        for i, name in enumerate(vehicle_names)
    ])

    # Display the full styled row
    st.markdown(f"""
    <div style='display: flex; justify-content: center; flex-wrap: wrap; font-size: 16px; font-weight: bold; text-align: center;'>
        {vehicle_html}
    </div>
    """, unsafe_allow_html=True)

    # --- Create 4 columns for selectboxes ---
    col1, col2, col3, col4 = st.columns(4)

    # --- Asset Category selectbox ---
    asset_categories = sorted(asset_mapping.keys())
    with col1:
        selected_category = st.selectbox(
            "Asset Category", asset_categories,
            index=asset_categories.index(st.session_state.asset_category)
        )
    if selected_category != st.session_state.asset_category:
        st.session_state.asset_category = selected_category
        st.session_state.asset_name = asset_mapping[selected_category][0]
        st.session_state.spare_type = ""
        st.session_state.position_spare = ""

    # --- Asset Name selectbox ---
    filtered_names = asset_mapping[st.session_state.asset_category]
    with col2:
        selected_name = st.selectbox(
            "Asset Name", filtered_names,
            index=filtered_names.index(st.session_state.asset_name)
        )
    if selected_name != st.session_state.asset_name:
        st.session_state.asset_name = selected_name

    # --- Failure/Scheduled Replacement of Spare selectbox ---
    comp_pos_list = category_components.get(st.session_state.asset_category, [])
    component_names = [comp for comp, _ in comp_pos_list]

    # Set default spare_type if not valid
    if st.session_state.spare_type not in component_names:
        st.session_state.spare_type = component_names[0] if component_names else ""

    with col3:
        spare_selection = st.selectbox(
            "Failure/Scheduled Replacement of Spare",
            options=component_names,
            index=component_names.index(st.session_state.spare_type)
        )
    if spare_selection != st.session_state.spare_type:
        st.session_state.spare_type = spare_selection
        st.session_state.position_spare = ""

    # --- Position selectbox ---
    position_options = next((pos_list for comp, pos_list in comp_pos_list if comp == st.session_state.spare_type), [])
    if not position_options:
        position_options = ["N/A"]

    if st.session_state.position_spare not in position_options:
        st.session_state.position_spare = position_options[0]

    with col4:
        position_selection = st.selectbox(
            "Position",
            options=position_options,
            index=position_options.index(st.session_state.position_spare)
        )
    if position_selection != st.session_state.position_spare:
        st.session_state.position_spare = position_selection


    st.markdown("---")
    st.markdown(
        "<div style='text-align: center; font-size:20px; font-weight: bold; color: red;'>🔧 Enter Other Details Below</div>",
        unsafe_allow_html=True
    )

    # Show headers as a single row above the inputs:
    detail_headers = [
        "Date From", "Date To", "Time From", "Time To", "Duration",
        "Failure HMR", "Old/New", "Previous HMR", "Life (hrs)", "Remarks", "SI Name"
    ]
    header_cols = st.columns(len(detail_headers))
    for col, header in zip(header_cols, detail_headers):
        col.markdown(f"**{header}**")

    # --- Form for the remaining fields ---

    from datetime import timedelta
    with st.form("entry_form"):
        input_cols = st.columns(11)

        st.session_state.date_from = input_cols[0].date_input("Date From", value=st.session_state.date_from, label_visibility="collapsed")
        st.session_state.date_to = input_cols[1].date_input("Date To", value=st.session_state.date_to, label_visibility="collapsed")
        
        #st.session_state.time_from = input_cols[2].time_input("Time From", value=st.session_state.time_from, label_visibility="collapsed")
        #st.session_state.time_to = input_cols[3].time_input("Time To", value=st.session_state.time_to, label_visibility="collapsed")

        st.session_state.time_from = input_cols[2].time_input(
        "Time From",
        value=st.session_state.time_from,
        step=timedelta(minutes=5),
        label_visibility="collapsed"
        )
    
        st.session_state.time_to = input_cols[3].time_input(
            "Time To",
            value=st.session_state.time_to,
            step=timedelta(minutes=5),
            label_visibility="collapsed"
        )













        
        # Duration calculation
        try:
            dt_from = datetime.combine(st.session_state.date_from, st.session_state.time_from)
            dt_to = datetime.combine(st.session_state.date_to, st.session_state.time_to)
        
            if dt_to < dt_from:
                raise ValueError("End datetime cannot be earlier than start datetime")
        
            duration = dt_to - dt_from
            total_minutes = int(duration.total_seconds() // 60)
            hours, minutes = divmod(total_minutes, 60)
            duration_str = f"{hours:02d}:{minutes:02d}"
        except Exception:
            duration_str = "Invalid"
        
        input_cols[4].text_input("Duration", value=duration_str, disabled=True, label_visibility="collapsed")

        st.session_state.failure_hmr = input_cols[5].text_input("Failure HMR", value=st.session_state.failure_hmr, label_visibility="collapsed")

        st.session_state.old_new = input_cols[6].selectbox("Old/New", ["Old", "New"], index=["Old", "New"].index(st.session_state.old_new), label_visibility="collapsed")
        st.session_state.prev_occ_hmr = input_cols[7].text_input("Previous HMR", value=st.session_state.prev_occ_hmr, label_visibility="collapsed")

        try:
            life_hrs_val = float(st.session_state.failure_hmr) - float(st.session_state.prev_occ_hmr)
            life_hrs_str = str(round(life_hrs_val, 2))
        except Exception:
            life_hrs_str = ""
        input_cols[8].text_input("Life (hrs)", value=life_hrs_str, disabled=True, label_visibility="collapsed")

        st.session_state.remarks = input_cols[9].text_input("Remarks", value=st.session_state.remarks, label_visibility="collapsed")
        st.session_state.si_name = input_cols[10].text_input("SI Name", value=st.session_state.si_name, label_visibility="collapsed")

        submitted = st.form_submit_button("Submit Entry")

        if submitted:
            try:
                failure_hmr_val = float(st.session_state.failure_hmr)
                prev_occ_hmr_val = float(st.session_state.prev_occ_hmr)
                life_hrs_val = failure_hmr_val - prev_occ_hmr_val
            except ValueError:
                st.error("❌ Failure HMR and Previous HMR must be numeric.")
            else:
                new_entry = pd.DataFrame([{
                    "Date From": st.session_state.date_from,
                    "Date To": st.session_state.date_to,
                    "Time From": st.session_state.time_from,
                    "Time To": st.session_state.time_to,
                    "Duration": duration_str,
                    "Failure HMR": failure_hmr_val,
                    "Asset Category": st.session_state.asset_category,
                    "Asset Name": st.session_state.asset_name,
                    "Failure/Scheduled": st.session_state.spare_type,
                    "Position": st.session_state.position_spare,
                    "Old/New": st.session_state.old_new,
                    "Previous HMR": prev_occ_hmr_val,
                    "Life (hrs)": round(life_hrs_val, 2),
                    "Remarks": st.session_state.remarks,
                    "SI Name": st.session_state.si_name
                }])
                df_existing = pd.concat([df_existing, new_entry], ignore_index=True)
                save_to_csv(df_existing)
                st.success("✅ Entry saved successfully!")

                # Reset form except dropdowns outside form
                st.session_state.date_from = datetime.today().date()
                st.session_state.date_to = datetime.today().date()
                st.session_state.time_from = time(10, 0)
                st.session_state.time_to = time(12, 0)
                st.session_state.failure_hmr = ""
                st.session_state.prev_occ_hmr = ""
                st.session_state.old_new = "New"
                st.session_state.remarks = ""
                st.session_state.si_name = ""

    # --- Show saved entries ---
    if not df_existing.empty:
        st.markdown(
            "<div style='text-align: center; font-size:20px; font-weight: bold; color: green;'>📋 Submitted Entries</div>",
            unsafe_allow_html=True
        )

        with st.expander("🔍 Filter Entries"):
        # Combine Date and Time into datetime for filtering
            df_existing['DateTime From'] = pd.to_datetime(
                df_existing['Date From'].astype(str) + ' ' + df_existing['Time From'].astype(str),
                errors='coerce'
            )
            df_existing['DateTime To'] = pd.to_datetime(
                df_existing['Date To'].astype(str) + ' ' + df_existing['Time To'].astype(str),
                errors='coerce'
            )

            # Remove rows with invalid combined timestamps
            df_existing = df_existing.dropna(subset=['DateTime From', 'DateTime To'])

            # Date range filter based on combined datetime
            min_date = df_existing['DateTime From'].min()
            max_date = df_existing['DateTime To'].max()
            start_date, end_date = st.date_input("Date Range", (min_date.date(), max_date.date()))
        
            # Step 2: Filter Asset Categories
            asset_categories = ["All"] + sorted(df_existing['Asset Category'].dropna().unique())
            selected_category = st.selectbox("Asset Category", asset_categories)
        
            # Step 3: Cascade Asset Name based on selected category
            if selected_category != "All":
                filtered_names = df_existing[df_existing['Asset Category'] == selected_category]['Asset Name'].dropna().unique()
            else:
                filtered_names = df_existing['Asset Name'].dropna().unique()
            asset_names = ["All"] + sorted(filtered_names)
            selected_name = st.selectbox("Asset Name", asset_names)
        
            # Step 4: Cascade Spare Type based on selected category and asset name
            df_temp = df_existing.copy()
            if selected_category != "All":
                df_temp = df_temp[df_temp['Asset Category'] == selected_category]
            if selected_name != "All":
                df_temp = df_temp[df_temp['Asset Name'] == selected_name]
        
            spare_types = ["All"] + sorted(df_temp['Failure/Scheduled'].dropna().unique())
            selected_spare = st.selectbox("Failure/Scheduled", spare_types)
        
            # Step 5: Apply All Filters
            df_filtered = df_existing[
                (df_existing['DateTime From'].dt.date >= start_date) &
                (df_existing['DateTime To'].dt.date <= end_date)
            ]
        
            if selected_category != "All":
                df_filtered = df_filtered[df_filtered['Asset Category'] == selected_category]
        
            if selected_name != "All":
                df_filtered = df_filtered[df_filtered['Asset Name'] == selected_name]
        
            if selected_spare != "All":
                df_filtered = df_filtered[df_filtered['Failure/Scheduled'] == selected_spare]
    
    # Sort and drop columns before display
        df_display = df_filtered.sort_values(by="DateTime From", ascending=False).drop(columns=["DateTime From", "DateTime To"])



        custom_css = """
<style>
.custom-table {
    border-collapse: collapse;
    width: 100%;
    font-family: Arial, sans-serif;
}
.custom-table th {
    background-color: #003366;
    color: white;
    font-weight: bold;
    padding: 8px;
    text-align: center;
}
.custom-table td {
    border: 1px solid #ddd;
    padding: 8px;
    text-align: center;
}
</style>
"""

# Convert DataFrame to HTML with the custom class
        html_table = df_display.to_html(index=False, classes="custom-table")
    
    # Display styled table
        st.markdown(custom_css + html_table, unsafe_allow_html=True)


        #st.dataframe(df_display, use_container_width=True)
        
        import win32com.client
        import pythoncom
        
        def send_email(df, subject, recipients):
            pythoncom.CoInitialize()
            df_html = df.to_html(index=False)
            outlook = win32com.client.Dispatch('outlook.application')
            mail = outlook.CreateItem(0)
            mail.Subject = subject
            mail.To = "; ".join(recipients)
            mail.HTMLBody = f"""
            <p>Hello Sir,</p>
            <p>Please find below the List of maintenance related entries in vehicles:</p>
            {df_html}
            <p>Best regards,</p>
            <p>Team Digital-Aditya Smelter</p>
            """
            mail.Send()
            pythoncom.CoUninitialize()
            print("Email sent successfully.")
        
        if st.button("Mail the Report") and not df_display.empty:
            subject = "Vehicle Maintenance: Maintenance Logger History card"
            recipients = ['<EMAIL>','<EMAIL>','<EMAIL>','<EMAIL>']
            send_email(df_display, subject, recipients)

with tab2:
     #st.set_page_config(page_title="Asset Failure Analytics", layout="wide")
     st.markdown(
    "<div style='text-align: center; font-size:22px; font-weight: bold; color: blue;'>🚧 Asset Failure Analytics</div>",
    unsafe_allow_html=True)
     
     df = df_existing.copy()

     def convert_duration(x):
        try:
            if isinstance(x, str) and ':' in x:
                h, m = map(int, x.split(":"))
                return h + m/60
            return float(x)
        except:
            return np.nan
    
     df['Duration_hr'] = df['Duration'].apply(convert_duration)
     tabs = st.tabs(["📄 Raw Data", "📊 Analytics"])
    
    # ============================ Tab 1: Raw Data
     with tabs[0]:
        st.subheader("Submitted Entries")
        st.dataframe(df_existing, use_container_width=True)
    
    # ============================ Tab 2: Analytics
     with tabs[1]:
        col1, col2 = st.columns(2)
        
        # 1️⃣ Failure Frequency Heatmap
        with col1:
            st.markdown("### 📊 Failure Frequency Heatmap (Asset Category vs Name)")
            heat_df = df.groupby(['Asset Category', 'Asset Name']).size().reset_index(name='Failure Count')
            fig_heat = px.density_heatmap(heat_df, x="Asset Category", y="Asset Name", z="Failure Count",
                                          color_continuous_scale="Reds")
            st.plotly_chart(fig_heat, use_container_width=True)
    
        # 2️⃣ MTBF and MTTR (Mean Time Between Failures & Mean Time To Repair)
        with col2:
            st.markdown("### 📉 MTBF & MTTR per Asset")
            mttr_df = df.groupby("Asset Name")['Duration_hr'].mean().reset_index().rename(columns={'Duration_hr': 'MTTR (hrs)'})
            df_sorted = df.sort_values(by=['Asset Name', 'DateTime From'])
            df_sorted['Previous Failure Date'] = df_sorted.groupby('Asset Name')['DateTime From'].shift(1)
            df_sorted['MTBF_days'] = (df_sorted['DateTime From'] - df_sorted['Previous Failure Date']).dt.total_seconds() / 86400
            mtbf_df = df_sorted.groupby('Asset Name')['MTBF_days'].mean().reset_index()
            metrics_df = pd.merge(mttr_df, mtbf_df, on='Asset Name')
            st.dataframe(metrics_df, use_container_width=True)
    
        # 3️⃣ Failure Trend Over Time
        st.markdown("### 🧭 Failure Trend Over Time (Monthly)")
        df['Month'] = df['DateTime From'].dt.to_period('M').astype(str)
        trend_df = df.groupby('Month').size().reset_index(name='Failure Count')
        fig_trend = px.line(trend_df, x='Month', y='Failure Count', markers=True, title="Failures Over Time")
        st.plotly_chart(fig_trend, use_container_width=True)
    
        # 4️⃣ Top 5 Frequent Failures
        st.markdown("### ⚠️ Top 5 Frequent Failures by Type")
        top_failure_df = df.groupby("Failure/Scheduled").agg({
            'Duration_hr': 'mean',
            'Failure HMR': 'mean',
            'Asset Name': 'count'
        }).rename(columns={'Asset Name': 'Count'}).reset_index()
    
        top5_df = top_failure_df.sort_values('Count', ascending=False).head(5)
        fig_bar = px.bar(top5_df, x="Failure/Scheduled", y="Count", color="Failure/Scheduled",
                         hover_data=["Duration_hr", "Failure HMR"],
                         title="Top 5 Failure Types with Avg Duration & HMR")
        st.plotly_chart(fig_bar, use_container_width=True)