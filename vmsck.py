import streamlit as st
import pandas as pd
from datetime import datetime
import os
import plotly.express as px
from collections import defaultdict
import numpy as np

# --- Page setup ---
st.set_page_config(layout="wide")

left_logo = r"C:\Users\<USER>\Downloads\images.jpg"
right_logo = r"C:\Users\<USER>\Downloads\images.jpg" 
col1, col2, col3 = st.columns([1, 6, 1])
with col1: 
    st.image(left_logo, width=120)
with col2:
    st.markdown("<h1 style='text-align: center; font-size: 30px;'>Equipment History & Replacement Schedule Management System</h1>", unsafe_allow_html=True)
with col3: 
    st.image(right_logo, width=120)

# --- Excel file for data source ---
EXCEL_FILE = "Equipment History & Replacement schedule_10062025.xlsx"

# --- Load data from Excel sheets ---
def load_assembly_type_mapping():
    """Load assembly to type mapping from Sheet1"""
    try:
        sheet1 = pd.read_excel(EXCEL_FILE, sheet_name='Sheet1', header=1)
        sheet1.columns = ['Assembly', 'Type']
        sheet1 = sheet1.dropna(subset=['Assembly'])
        sheet1 = sheet1[sheet1['Assembly'] != 'Row Labels']
        return dict(zip(sheet1['Assembly'], sheet1['Type']))
    except Exception as e:
        st.error(f"Error reading Sheet1: {e}")
        return {}

def load_assemblies_data():
    """Load assemblies data from Assemblies sheet"""
    try:
        assemblies = pd.read_excel(EXCEL_FILE, sheet_name='Assebmlies', header=1)
        assembly_components = defaultdict(list)
        
        # Process columns in groups of 3 (component, freq, cat)
        for col_idx in range(1, len(assemblies.columns), 3):
            if col_idx < len(assemblies.columns):
                component_col = assemblies.columns[col_idx]
                components = assemblies[component_col].dropna().tolist()
                
                # Get category name from first row or column header
                category = str(component_col).replace('Unnamed: ', '').strip()
                if len(components) > 0 and str(components[0]).strip():
                    category = str(components[0]).strip()
                    components = components[1:]  # Remove header
                
                if category and components:
                    # Clean components list
                    components = [comp for comp in components if comp not in ['Freq', 'Cat'] and str(comp).strip()]
                    if components:
                        assembly_components[category] = components
        
        return dict(assembly_components)
    except Exception as e:
        st.error(f"Error reading Assemblies sheet: {e}")
        return {}

def load_maintenance_data():
    """Load existing maintenance data"""
    relevant_columns = ['Date', 'Equipment', 'Assembly', 'Component', 'Type', 'Category']
    return pd.DataFrame(columns=relevant_columns)

@st.cache_data
def load_replacement_data():
    """Load replacement schedule data from Excel with caching"""
    try:
        # Try 'Replacement ' (with space) first, then 'Replacement' (without space)
        replacement_df = None

        # Quick attempt with most likely header
        try:
            replacement_df = pd.read_excel(EXCEL_FILE, sheet_name='Replacement ', header=0)
        except:
            try:
                replacement_df = pd.read_excel(EXCEL_FILE, sheet_name='Replacement', header=0)
            except:
                return pd.DataFrame()

        if replacement_df is not None and not replacement_df.empty:
            # Quick cleanup - remove completely empty rows and columns
            replacement_df = replacement_df.dropna(how='all').dropna(axis=1, how='all')

            # Simple column name fix if needed
            if replacement_df.columns[0] == 'Unnamed: 0':
                # Use first row as headers if current headers are unnamed
                if not replacement_df.empty:
                    new_columns = []
                    for i, _ in enumerate(replacement_df.columns):
                        try:
                            first_val = str(replacement_df.iloc[0, i])
                            if first_val and first_val != 'nan' and first_val.strip():
                                new_columns.append(first_val.strip())
                            else:
                                new_columns.append(f"Column_{i+1}")
                        except:
                            new_columns.append(f"Column_{i+1}")

                    replacement_df.columns = new_columns
                    replacement_df = replacement_df.iloc[1:].reset_index(drop=True)

        return replacement_df if replacement_df is not None else pd.DataFrame()

    except Exception as e:
        st.error(f"Error reading Replacement sheet: {e}")
        return pd.DataFrame()

# Load Excel data mappings
@st.cache_data
def load_excel_mappings():
    assembly_type_mapping = load_assembly_type_mapping()
    assemblies_data = load_assemblies_data()
    return assembly_type_mapping, assemblies_data

# Initialize session state
if 'df_existing' not in st.session_state:
    st.session_state.df_existing = load_maintenance_data()

if 'assembly_type_mapping' not in st.session_state or 'assemblies_data' not in st.session_state:
    st.session_state.assembly_type_mapping, st.session_state.assemblies_data = load_excel_mappings()

# Manual refresh button
if st.button("🔄 Refresh Data from Excel"):
    load_excel_mappings.clear()
    st.session_state.assembly_type_mapping, st.session_state.assemblies_data = load_excel_mappings()
    st.success("✅ Data mappings refreshed from Excel file")

df_existing = st.session_state.df_existing
assembly_type_mapping = st.session_state.assembly_type_mapping
assemblies_data = st.session_state.assemblies_data

# Display Excel data mappings
if st.checkbox("🔍 Show Excel Data Mappings"):
    col1, col2 = st.columns(2)
    
    with col1:
        st.write("### 📊 Assembly Type Mapping")
        if assembly_type_mapping:
            mapping_df = pd.DataFrame(list(assembly_type_mapping.items()), columns=['Assembly', 'Type'])
            st.dataframe(mapping_df.head(10))
            st.write(f"Total assemblies: {len(assembly_type_mapping)}")
        else:
            st.write("No assembly type mapping found")
    
    with col2:
        st.write("### 🔧 Assembly Components")
        if assemblies_data:
            for category, components in list(assemblies_data.items())[:3]:
                if components:
                    st.write(f"**{category}:** {len(components)} components")
        else:
            st.write("No assembly components found")

def save_to_excel(df):
    """Save maintenance data"""
    try:
        if not df.empty and 'Date' in df.columns:
            df['Date'] = pd.to_datetime(df['Date'])
        # For now, just update session state
        st.session_state.df_existing = df
        st.success("✅ Data saved to session")
        return True
    except Exception as e:
        st.error(f"Error saving data: {e}")
        return False

tab1, tab2, tab3, tab4 = st.tabs(["Maintenance Form", "Replacement Schedule", "Last Replacement Check", "Analytics Overview"])

with tab1:
    st.markdown(
        "<div style='text-align: center; font-size:22px; font-weight: bold; color: blue;'>🔧 Equipment Maintenance & Replacement Schedule</div>",
        unsafe_allow_html=True
    )

    # Equipment list
    equipment_list = ["PTA 1", "PTA 2", "PTA 3", "PTA 4", "PTA 5", "PTA 6", "PTA 7", "PTA 8", "PTA 9"]
    
    # Get assemblies and components from Excel data
    available_assemblies = list(assembly_type_mapping.keys()) if assembly_type_mapping else []
    all_components = []
    for category, components in assemblies_data.items():
        all_components.extend(components)

    # Initialize session state
    form_keys = {
        "maintenance_date": datetime.today().date(),
        "equipment_name": equipment_list[0] if equipment_list else "",
        "selected_assembly": "",
        "selected_component": "",
        "maintenance_type": ""
    }

    for k, v in form_keys.items():
        if k not in st.session_state:
            st.session_state[k] = v

    # Display equipment names
    colors = ["#1a73e8", "#d93025", "#188038", "#f9ab00", "#6f42c1", "#e37400", "#008080", "#ff6f61", "#4caf50", "#d81b60"]
    cols = st.columns(len(equipment_list))
    for i, (col, name) in enumerate(zip(cols, equipment_list)):
        color = colors[i % len(colors)]
        col.markdown(
            f"<div style='background-color: {color}; color: white; padding: 10px; text-align: center; border-radius: 5px; margin: 2px;'>{name}</div>",
            unsafe_allow_html=True
        )

    # Form inputs
    st.write("### 📝 Maintenance Entry Form")

    # Row 1: Date field
    st.session_state.maintenance_date = st.date_input("Maintenance Date", value=st.session_state.maintenance_date)

    # Row 2: Equipment and Assembly selection
    col1, col2, col3 = st.columns(3)
    with col1:
        st.session_state.equipment_name = st.selectbox("Equipment", equipment_list,
                                                      index=equipment_list.index(st.session_state.equipment_name) if st.session_state.equipment_name in equipment_list else 0)
    with col2:
        st.session_state.selected_assembly = st.selectbox("Assembly", [""] + available_assemblies,
                                                          index=0 if not st.session_state.selected_assembly else available_assemblies.index(st.session_state.selected_assembly) + 1 if st.session_state.selected_assembly in available_assemblies else 0)
    with col3:
        # Get maintenance type from assembly mapping
        if st.session_state.selected_assembly and st.session_state.selected_assembly in assembly_type_mapping:
            mapped_type = assembly_type_mapping[st.session_state.selected_assembly]
            type_display = f"Routine (R)" if mapped_type == "R" else f"Non-Routine (NR)" if mapped_type == "NR" else mapped_type
            st.session_state.maintenance_type = st.selectbox("Maintenance Type", [type_display], index=0)
        else:
            st.session_state.maintenance_type = st.selectbox("Maintenance Type", ["Routine (R)", "Non-Routine (NR)", "Emergency", "Preventive"])

    # Row 3: Component selection
    # Filter components based on selected assembly category
    filtered_components = []
    if st.session_state.selected_assembly:
        for category, components in assemblies_data.items():
            if st.session_state.selected_assembly in components or category.lower() in st.session_state.selected_assembly.lower():
                filtered_components.extend(components)

    if not filtered_components:
        filtered_components = all_components

    st.session_state.selected_component = st.selectbox("Component", [""] + filtered_components[:50])  # Limit for performance

    # Submit button
    if st.button("💾 Submit Maintenance Entry", type="primary"):
        if st.session_state.equipment_name and st.session_state.selected_assembly:
            entry_data = {
                "Date": pd.Timestamp(st.session_state.maintenance_date),
                "Equipment": st.session_state.equipment_name,
                "Assembly": st.session_state.selected_assembly,
                "Component": st.session_state.selected_component,
                "Type": st.session_state.maintenance_type,
                "Category": assembly_type_mapping.get(st.session_state.selected_assembly, "")
            }

            new_entry = pd.DataFrame([entry_data])
            st.session_state.df_existing = pd.concat([st.session_state.df_existing, new_entry], ignore_index=True)
            save_to_excel(st.session_state.df_existing)
            st.success("✅ Maintenance entry submitted successfully!")

            # Reset form
            for key in form_keys.keys():
                if key != "maintenance_date":
                    st.session_state[key] = form_keys[key]
            st.rerun()
        else:
            st.error("❌ Please fill in Equipment and Assembly fields")

    # Show latest entries
    if not df_existing.empty:
        st.write("### 📋 Latest 5 Maintenance Entries")
        display_df = df_existing.sort_values(by="Date", ascending=False).head(5)
        st.dataframe(display_df)

with tab2:
    st.markdown(
        "<div style='text-align: center; font-size:22px; font-weight: bold; color: blue;'>📅 Equipment Replacement Schedule</div>",
        unsafe_allow_html=True
    )

    # Use cached data loading
    with st.spinner("Loading replacement data..."):
        replacement_data = load_replacement_data()

    if not replacement_data.empty:
        st.success(f"✅ Loaded {len(replacement_data)} replacement records")

        # Quick display with limited rows for performance
        st.write("### 📋 Replacement Schedule Data (First 20 rows)")
        display_data = replacement_data.head(20)

        # Simple filter for better performance
        if 'Equip' in replacement_data.columns:
            col1, col2 = st.columns(2)
            with col1:
                equipment_filter = st.selectbox(
                    "Filter by Equipment:",
                    ["All"] + sorted(replacement_data['Equip'].dropna().unique().tolist()[:20])  # Limit options
                )

            if equipment_filter != "All":
                display_data = replacement_data[replacement_data['Equip'] == equipment_filter].head(20)
                with col2:
                    st.metric("Filtered Records", len(display_data))

        # Display the data
        st.dataframe(display_data, use_container_width=True, height=400)

        # Quick summary
        col1, col2, col3 = st.columns(3)
        with col1:
            st.metric("Total Records", len(replacement_data))
        with col2:
            if 'Equip' in replacement_data.columns:
                st.metric("Unique Equipment", replacement_data['Equip'].nunique())
        with col3:
            if 'Assembly' in replacement_data.columns:
                st.metric("Unique Assemblies", replacement_data['Assembly'].nunique())

    else:
        st.warning("⚠️ No replacement schedule data found.")

        # Quick sheet check
        try:
            xl_file = pd.ExcelFile(EXCEL_FILE)
            st.write(f"**Available sheets:** {xl_file.sheet_names}")
        except Exception as e:
            st.error(f"Error: {e}")

with tab3:
    st.markdown(
        "<div style='text-align: center; font-size:22px; font-weight: bold; color: blue;'>🔍 Last Replacement Check</div>",
        unsafe_allow_html=True
    )

    # Use cached data and optimize loading
    if 'replacement_data_cached' not in st.session_state:
        with st.spinner("Loading replacement data..."):
            st.session_state.replacement_data_cached = load_replacement_data()

    replacement_data = st.session_state.replacement_data_cached

    if not replacement_data.empty:
        st.write("### 🔧 Check Last Replacement Date")

        # Pre-compute options for faster dropdown loading
        if 'equipment_options' not in st.session_state:
            if 'Equip' in replacement_data.columns:
                st.session_state.equipment_options = sorted(replacement_data['Equip'].dropna().unique().tolist())
            else:
                st.session_state.equipment_options = []

        if 'assembly_options' not in st.session_state:
            if 'Assembly' in replacement_data.columns:
                st.session_state.assembly_options = sorted(replacement_data['Assembly'].dropna().unique().tolist())
            else:
                st.session_state.assembly_options = []

        # Create selection dropdowns with cached options
        col1, col2 = st.columns(2)

        with col1:
            selected_equipment = st.selectbox(
                "Select Equipment:",
                [""] + st.session_state.equipment_options,
                help="Choose the equipment to check"
            )

        with col2:
            # Filter assemblies based on selected equipment for better UX
            if selected_equipment:
                filtered_assemblies = replacement_data[replacement_data['Equip'] == selected_equipment]['Assembly'].dropna().unique().tolist()
                assembly_options_filtered = sorted(filtered_assemblies)
            else:
                assembly_options_filtered = st.session_state.assembly_options

            selected_assembly = st.selectbox(
                "Select Assembly:",
                [""] + assembly_options_filtered,
                help="Choose the assembly to check"
            )

        # Submit button
        if st.button("🔍 Check Last Replacement Date", type="primary"):
            if selected_equipment and selected_assembly:
                # Filter data based on selection
                filtered_data = replacement_data[
                    (replacement_data['Equip'] == selected_equipment) &
                    (replacement_data['Assembly'] == selected_assembly)
                ]

                if not filtered_data.empty:
                    # Find all maintenance date columns
                    maintenance_date_columns = [col for col in replacement_data.columns if 'Maintenance date' in col]

                    # Get all dates for this equipment/assembly combination
                    all_dates = []
                    for _, row in filtered_data.iterrows():
                        for date_col in maintenance_date_columns:
                            date_val = row[date_col]
                            if pd.notna(date_val) and date_val != '':
                                try:
                                    # Try to parse the date
                                    parsed_date = pd.to_datetime(date_val, errors='coerce')
                                    if pd.notna(parsed_date):
                                        all_dates.append(parsed_date)
                                except:
                                    continue

                    # Display results
                    st.write("### 📊 Results")

                    if all_dates:
                        latest_date = max(all_dates)
                        st.success(f"✅ **Latest Replacement Date:** {latest_date.strftime('%Y-%m-%d')}")

                        # Show additional information
                        col1, col2, col3 = st.columns(3)
                        with col1:
                            st.metric("Equipment", selected_equipment)
                        with col2:
                            st.metric("Assembly", selected_assembly)
                        with col3:
                            days_since = (datetime.now() - latest_date).days
                            st.metric("Days Since Last Replacement", days_since)

                        # Show all replacement dates
                        if len(all_dates) > 1:
                            st.write("### 📅 All Replacement Dates")
                            sorted_dates = sorted(all_dates, reverse=True)
                            for i, date in enumerate(sorted_dates[:10]):  # Show last 10 dates
                                st.write(f"{i+1}. {date.strftime('%Y-%m-%d')}")

                    else:
                        st.warning("⚠️ No replacement dates found for this equipment and assembly combination.")

                        # Show the filtered data for debugging
                        st.write("### 📋 Available Data")
                        st.dataframe(filtered_data)

                else:
                    st.error("❌ No data found for the selected equipment and assembly combination.")

            else:
                st.error("❌ Please select both Equipment and Assembly.")

        # Show summary statistics
        if st.session_state.equipment_options and st.session_state.assembly_options:
            st.write("### 📈 Summary")
            col1, col2 = st.columns(2)
            with col1:
                st.metric("Total Equipment", len(st.session_state.equipment_options))
            with col2:
                st.metric("Total Assemblies", len(st.session_state.assembly_options))

    else:
        st.warning("⚠️ No replacement data available for checking.")

with tab4:
    st.markdown("### 📊 Analytics Overview")
    st.write("Analytics features will be implemented here")

    if not df_existing.empty:
        st.write(f"Total maintenance entries: {len(df_existing)}")

        # Show type distribution
        if 'Type' in df_existing.columns:
            type_counts = df_existing['Type'].value_counts()
            fig = px.pie(values=type_counts.values, names=type_counts.index, title="Maintenance Type Distribution")
            st.plotly_chart(fig)
    else:
        st.write("No data available for analytics")
